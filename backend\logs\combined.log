{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 12:57:24"}
{"level":"info","message":"Server running on port 3002","service":"whatsapp-api","timestamp":"2025-08-08 12:57:24"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 12:57:24"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 12:57:24"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:57:31"}
{"level":"info","message":"GET /auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:57:34"}
{"level":"info","message":"GET /auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:57:34"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:57:35"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:57:45"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:58:26"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:58:31"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:58:51"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:59:03"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:59:11"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:59:31"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 12:59:40"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 12:59:51"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"whatsapp-api","timestamp":"2025-08-08 13:00:06"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:05:24"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:05:24"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:05:24"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:05:24"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:05:29"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:31"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:31"}
{"level":"info","message":"GET / - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:37"}
{"level":"info","message":"GET / - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:39"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:39"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:48"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:05:59"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:06:29"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:06:29"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:06:29"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:06:49"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:07:09"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:07:13"}
{"level":"info","message":"GET /api/auth - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:07:23"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:07:23"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:07:29"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"whatsapp-api","timestamp":"2025-08-08 13:07:30"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:07:37"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:07:37"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:07:37"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:07:37"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:07:42"}
{"level":"info","message":"GET /api/auth - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:07:45"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:08:04"}
{"level":"info","message":"GET / - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:08:07"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:08:11"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:08:20"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:08:42"}
{"level":"info","message":"GET /api/ - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:08:54"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:09:02"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:09:22"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:09:41"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:09:42"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:10:02"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:11:13"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:11:43"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:11:43"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:11:44"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:11:44"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"whatsapp-api","timestamp":"2025-08-08 13:11:47"}
{"level":"info","message":"POST /api/auth/login - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:24"}
{"level":"error","message":"Login error: secretOrPrivateKey must have a value","service":"whatsapp-api","stack":"Error: secretOrPrivateKey must have a value\n    at Object.<anonymous>.module.exports [as sign] (D:\\waweb.js\\backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at sign (D:\\waweb.js\\backend\\src\\routes\\auth.js:57:23)","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/auth/login - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Login error: secretOrPrivateKey must have a value","service":"whatsapp-api","stack":"Error: secretOrPrivateKey must have a value\n    at Object.<anonymous>.module.exports [as sign] (D:\\waweb.js\\backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at sign (D:\\waweb.js\\backend\\src\\routes\\auth.js:57:23)","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/auth/login - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/auth/verify - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/auth/qr - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"QR code error: Cannot read properties of undefined (reading 'getQRCode')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'getQRCode')\n    at getQRCode (D:\\waweb.js\\backend\\src\\routes\\auth.js:94:36)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status/health - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/messages/send - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/messages/send - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/messages/send - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/messages/scheduled - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/contacts - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/contacts - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"POST /api/contacts/check - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/groups - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/groups - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"JWT_SECRET not configured","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/status - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"level":"error","message":"Status check error: Cannot read properties of undefined (reading 'isReady')","service":"whatsapp-api","stack":"TypeError: Cannot read properties of undefined (reading 'isReady')\n    at isReady (D:\\waweb.js\\backend\\src\\routes\\status.js:16:32)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at next (D:\\waweb.js\\backend\\src\\app.js:67:3)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at urlencodedParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at jsonParser (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:113:7)\n    at Layer.handle [as handle_request] (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\waweb.js\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at D:\\waweb.js\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-08-08 13:12:25"}
{"level":"info","message":"GET /api/nonexistent - ::ffff:127.0.0.1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:25"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error occurred: Unexpected token 'i', \"invalid json\" is not valid JSON","method":"POST","service":"whatsapp-api","stack":"SyntaxError: Unexpected token 'i', \"invalid json\" is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:169:10)\n    at parse (D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\types\\json.js:86:15)\n    at D:\\waweb.js\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (D:\\waweb.js\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (D:\\waweb.js\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (D:\\waweb.js\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-08-08 13:12:25","url":"/api/auth/login"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:12:30"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:12:30"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:12:30"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:12:30"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:12:34"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:36"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:44"}
{"level":"info","message":"GET /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:46"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:12:49"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:13:03"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:13:34"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:14:17"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:14:17"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:14:17"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:14:17"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:14:22"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:14:22"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:14:37"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:14:43"}
{"level":"info","message":"GET /api/status - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:14:47"}
{"level":"info","message":"GET /api/status - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:14:53"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:15:22"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:25"}
{"level":"info","message":"GET /api - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"GET /health - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"GET /api/status - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"GET /api/auth/qr - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:15:28"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:15:42"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:15:44"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:16:02"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:16:16"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:16:22"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:16:42"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:16:44"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:16:46"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:17:53"}
{"level":"info","message":"POST /auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:18:51"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:18:53"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:19:09"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:19:09"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:19:13"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:19:33"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:19:53"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:20:13"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:20:44"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:20:44"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:21:00"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:21:00"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:21:24"}
{"level":"info","message":"POST /api/auth/logout - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:21:46"}
{"level":"info","message":"WhatsApp client logged out","service":"whatsapp-api","timestamp":"2025-08-08 13:21:46"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:21:49"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:21:49"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:23:12"}
{"level":"info","message":"Client connected: HK17m3y6DlMcUTwFAAAF","service":"whatsapp-api","timestamp":"2025-08-08 13:23:12"}
{"level":"info","message":"Client disconnected: HK17m3y6DlMcUTwFAAAF","service":"whatsapp-api","timestamp":"2025-08-08 13:23:28"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:23:29"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:23:29"}
{"level":"info","message":"Client connected: YpkW0_LXFA1SsgvQAAAI","service":"whatsapp-api","timestamp":"2025-08-08 13:23:29"}
{"level":"info","message":"Client disconnected: YpkW0_LXFA1SsgvQAAAI","service":"whatsapp-api","timestamp":"2025-08-08 13:23:48"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:24:07"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:24:07"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:24:07"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:24:07"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:24:11"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:24:54"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:24:54"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:24:54"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:24:54"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:24:59"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:25:58"}
{"level":"info","message":"Client connected: eTJybComz5Sid6o4AAAB","service":"whatsapp-api","timestamp":"2025-08-08 13:26:06"}
{"level":"info","message":"Client disconnected: eTJybComz5Sid6o4AAAB","service":"whatsapp-api","timestamp":"2025-08-08 13:26:11"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:26:18"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:26:25"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:26:25"}
{"level":"info","message":"Client connected: bOPzzXYPxirVH5ABAAAD","service":"whatsapp-api","timestamp":"2025-08-08 13:26:25"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:26:38"}
{"level":"info","message":"Client disconnected: bOPzzXYPxirVH5ABAAAD","service":"whatsapp-api","timestamp":"2025-08-08 13:26:38"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:26:51"}
{"level":"info","message":"GET /api/auth/verify - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:26:51"}
{"level":"info","message":"Client connected: j-SOz0lOotNBYYBQAAAG","service":"whatsapp-api","timestamp":"2025-08-08 13:26:51"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:26:58"}
{"level":"info","message":"Client disconnected: j-SOz0lOotNBYYBQAAAG","service":"whatsapp-api","timestamp":"2025-08-08 13:26:58"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:27:10"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:27:10"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:27:10"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:27:10"}
{"level":"info","message":"Scheduler service initialized","service":"whatsapp-api","timestamp":"2025-08-08 13:27:20"}
{"level":"info","message":"Server running on port 3001","service":"whatsapp-api","timestamp":"2025-08-08 13:27:20"}
{"level":"info","message":"Environment: development","service":"whatsapp-api","timestamp":"2025-08-08 13:27:20"}
{"level":"info","message":"Initializing WhatsApp client...","service":"whatsapp-api","timestamp":"2025-08-08 13:27:20"}
{"level":"info","message":"POST /api/auth/login - ::1","service":"whatsapp-api","timestamp":"2025-08-08 13:27:24"}
{"level":"info","message":"User admin logged in successfully","service":"whatsapp-api","timestamp":"2025-08-08 13:27:24"}
{"level":"info","message":"Client connected: aDZmuU6ZtvO2QUMRAAAB","service":"whatsapp-api","timestamp":"2025-08-08 13:27:24"}
{"level":"info","message":"QR Code received","service":"whatsapp-api","timestamp":"2025-08-08 13:27:24"}
{"level":"info","message":"Client disconnected: aDZmuU6ZtvO2QUMRAAAB","service":"whatsapp-api","timestamp":"2025-08-08 13:27:24"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"whatsapp-api","timestamp":"2025-08-08 13:27:41"}
