const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');

// Import routes
const authRoutes = require('./routes/auth');
const messageRoutes = require('./routes/messages');
const contactRoutes = require('./routes/contacts');
const groupRoutes = require('./routes/groups');
const statusRoutes = require('./routes/status');

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));

// Compression middleware
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files for uploads
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API base route - helpful information
app.get('/api', (req, res) => {
  res.json({
    name: 'WhatsApp API Gateway',
    version: '1.0.0',
    description: 'Unofficial WhatsApp API Gateway using whatsapp-web.js',
    endpoints: {
      authentication: {
        'POST /api/auth/login': 'Login and get JWT token',
        'GET /api/auth/qr': 'Get QR code for WhatsApp authentication',
        'GET /api/auth/verify': 'Verify JWT token',
        'POST /api/auth/logout': 'Logout from WhatsApp'
      },
      status: {
        'GET /api/status': 'Get WhatsApp client status',
        'GET /api/status/health': 'Health check endpoint'
      },
      messages: {
        'POST /api/messages/send': 'Send a text message (requires auth)',
        'POST /api/messages/send-media': 'Send a media message (requires auth)',
        'GET /api/messages/{chatId}': 'Get messages from a chat (requires auth)',
        'POST /api/messages/schedule': 'Schedule a message (requires auth)',
        'GET /api/messages/scheduled': 'Get scheduled messages (requires auth)'
      },
      contacts: {
        'GET /api/contacts': 'Get all contacts (requires auth)',
        'GET /api/contacts/{id}': 'Get specific contact (requires auth)',
        'POST /api/contacts/check': 'Check if phone number is on WhatsApp (requires auth)'
      },
      groups: {
        'GET /api/groups': 'Get all groups/chats (requires auth)',
        'POST /api/groups': 'Create a new group (requires auth)',
        'PUT /api/groups/{id}': 'Update group information (requires auth)'
      }
    },
    documentation: 'See /docs/api-documentation.md for detailed API documentation',
    websocket: 'Connect to ws://localhost:3001 for real-time updates',
    timestamp: new Date().toISOString()
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/status', statusRoutes);

// Protected routes (require authentication)
app.use('/api/messages', authMiddleware, messageRoutes);
app.use('/api/contacts', authMiddleware, contactRoutes);
app.use('/api/groups', authMiddleware, groupRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

module.exports = app;
