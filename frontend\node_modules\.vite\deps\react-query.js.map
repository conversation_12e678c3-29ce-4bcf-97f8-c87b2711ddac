{"version": 3, "sources": ["../../react-query/es/core/subscribable.js", "../../react-query/es/core/utils.js", "../../react-query/es/core/focusManager.js", "../../react-query/es/core/onlineManager.js", "../../react-query/es/core/retryer.js", "../../react-query/es/core/query.js", "../../react-query/es/core/notifyManager.js", "../../react-query/es/core/logger.js", "../../react-query/es/core/queryCache.js", "../../react-query/es/core/queryClient.js", "../../react-query/es/core/mutation.js", "../../react-query/es/core/mutationCache.js", "../../react-query/es/core/infiniteQueryBehavior.js", "../../react-query/es/core/queryObserver.js", "../../react-query/es/core/queriesObserver.js", "../../react-query/es/core/infiniteQueryObserver.js", "../../react-query/es/core/mutationObserver.js", "../../react-query/es/core/hydration.js", "../../react-query/es/react/reactBatchedUpdates.js", "../../react-query/es/react/setBatchUpdatesFn.js", "../../react-query/es/react/logger.js", "../../react-query/es/react/setLogger.js", "../../react-query/es/react/QueryClientProvider.js", "../../react-query/es/react/QueryErrorResetBoundary.js", "../../react-query/es/react/useIsFetching.js", "../../react-query/es/react/useIsMutating.js", "../../react-query/es/react/useMutation.js", "../../react-query/es/react/utils.js", "../../react-query/es/react/useBaseQuery.js", "../../react-query/es/react/useQuery.js", "../../react-query/es/react/useQueries.js", "../../react-query/es/react/useInfiniteQuery.js", "../../react-query/es/react/Hydrate.js"], "sourcesContent": ["export var Subscribable = /*#__PURE__*/function () {\n  function Subscribable() {\n    this.listeners = [];\n  }\n\n  var _proto = Subscribable.prototype;\n\n  _proto.subscribe = function subscribe(listener) {\n    var _this = this;\n\n    var callback = listener || function () {\n      return undefined;\n    };\n\n    this.listeners.push(callback);\n    this.onSubscribe();\n    return function () {\n      _this.listeners = _this.listeners.filter(function (x) {\n        return x !== callback;\n      });\n\n      _this.onUnsubscribe();\n    };\n  };\n\n  _proto.hasListeners = function hasListeners() {\n    return this.listeners.length > 0;\n  };\n\n  _proto.onSubscribe = function onSubscribe() {// Do nothing\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {// Do nothing\n  };\n\n  return Subscribable;\n}();", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// TYPES\n// UTILS\nexport var isServer = typeof window === 'undefined';\nexport function noop() {\n  return undefined;\n}\nexport function functionalUpdate(updater, input) {\n  return typeof updater === 'function' ? updater(input) : updater;\n}\nexport function isValidTimeout(value) {\n  return typeof value === 'number' && value >= 0 && value !== Infinity;\n}\nexport function ensureQueryKeyArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nexport function difference(array1, array2) {\n  return array1.filter(function (x) {\n    return array2.indexOf(x) === -1;\n  });\n}\nexport function replaceAt(array, index, value) {\n  var copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nexport function timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nexport function parseQueryArgs(arg1, arg2, arg3) {\n  if (!isQuery<PERSON>ey(arg1)) {\n    return arg1;\n  }\n\n  if (typeof arg2 === 'function') {\n    return _extends({}, arg3, {\n      queryKey: arg1,\n      queryFn: arg2\n    });\n  }\n\n  return _extends({}, arg2, {\n    queryKey: arg1\n  });\n}\nexport function parseMutationArgs(arg1, arg2, arg3) {\n  if (isQueryKey(arg1)) {\n    if (typeof arg2 === 'function') {\n      return _extends({}, arg3, {\n        mutationKey: arg1,\n        mutationFn: arg2\n      });\n    }\n\n    return _extends({}, arg2, {\n      mutationKey: arg1\n    });\n  }\n\n  if (typeof arg1 === 'function') {\n    return _extends({}, arg2, {\n      mutationFn: arg1\n    });\n  }\n\n  return _extends({}, arg1);\n}\nexport function parseFilterArgs(arg1, arg2, arg3) {\n  return isQueryKey(arg1) ? [_extends({}, arg2, {\n    queryKey: arg1\n  }), arg3] : [arg1 || {}, arg2];\n}\nexport function parseMutationFilterArgs(arg1, arg2) {\n  return isQueryKey(arg1) ? _extends({}, arg2, {\n    mutationKey: arg1\n  }) : arg1;\n}\nexport function mapQueryStatusFilter(active, inactive) {\n  if (active === true && inactive === true || active == null && inactive == null) {\n    return 'all';\n  } else if (active === false && inactive === false) {\n    return 'none';\n  } else {\n    // At this point, active|inactive can only be true|false or false|true\n    // so, when only one value is provided, the missing one has to be the negated value\n    var isActive = active != null ? active : !inactive;\n    return isActive ? 'active' : 'inactive';\n  }\n}\nexport function matchQuery(filters, query) {\n  var active = filters.active,\n      exact = filters.exact,\n      fetching = filters.fetching,\n      inactive = filters.inactive,\n      predicate = filters.predicate,\n      queryKey = filters.queryKey,\n      stale = filters.stale;\n\n  if (isQueryKey(queryKey)) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n\n  var queryStatusFilter = mapQueryStatusFilter(active, inactive);\n\n  if (queryStatusFilter === 'none') {\n    return false;\n  } else if (queryStatusFilter !== 'all') {\n    var isActive = query.isActive();\n\n    if (queryStatusFilter === 'active' && !isActive) {\n      return false;\n    }\n\n    if (queryStatusFilter === 'inactive' && isActive) {\n      return false;\n    }\n  }\n\n  if (typeof stale === 'boolean' && query.isStale() !== stale) {\n    return false;\n  }\n\n  if (typeof fetching === 'boolean' && query.isFetching() !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n\n  return true;\n}\nexport function matchMutation(filters, mutation) {\n  var exact = filters.exact,\n      fetching = filters.fetching,\n      predicate = filters.predicate,\n      mutationKey = filters.mutationKey;\n\n  if (isQueryKey(mutationKey)) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n\n    if (exact) {\n      if (hashQueryKey(mutation.options.mutationKey) !== hashQueryKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n\n  if (typeof fetching === 'boolean' && mutation.state.status === 'loading' !== fetching) {\n    return false;\n  }\n\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n\n  return true;\n}\nexport function hashQueryKeyByOptions(queryKey, options) {\n  var hashFn = (options == null ? void 0 : options.queryKeyHashFn) || hashQueryKey;\n  return hashFn(queryKey);\n}\n/**\n * Default query keys hash function.\n */\n\nexport function hashQueryKey(queryKey) {\n  var asArray = ensureQueryKeyArray(queryKey);\n  return stableValueHash(asArray);\n}\n/**\n * Hashes the value into a stable hash.\n */\n\nexport function stableValueHash(value) {\n  return JSON.stringify(value, function (_, val) {\n    return isPlainObject(val) ? Object.keys(val).sort().reduce(function (result, key) {\n      result[key] = val[key];\n      return result;\n    }, {}) : val;\n  });\n}\n/**\n * Checks if key `b` partially matches with key `a`.\n */\n\nexport function partialMatchKey(a, b) {\n  return partialDeepEqual(ensureQueryKeyArray(a), ensureQueryKeyArray(b));\n}\n/**\n * Checks if `b` partially matches with `a`.\n */\n\nexport function partialDeepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (typeof a !== typeof b) {\n    return false;\n  }\n\n  if (a && b && typeof a === 'object' && typeof b === 'object') {\n    return !Object.keys(b).some(function (key) {\n      return !partialDeepEqual(a[key], b[key]);\n    });\n  }\n\n  return false;\n}\n/**\n * This function returns `a` if `b` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between JSON values for example.\n */\n\nexport function replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n\n  var array = Array.isArray(a) && Array.isArray(b);\n\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    var aSize = array ? a.length : Object.keys(a).length;\n    var bItems = array ? b : Object.keys(b);\n    var bSize = bItems.length;\n    var copy = array ? [] : {};\n    var equalItems = 0;\n\n    for (var i = 0; i < bSize; i++) {\n      var key = array ? i : bItems[i];\n      copy[key] = replaceEqualDeep(a[key], b[key]);\n\n      if (copy[key] === a[key]) {\n        equalItems++;\n      }\n    }\n\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n\n  return b;\n}\n/**\n * Shallow compare objects. Only works with objects that always have the same properties.\n */\n\nexport function shallowEqualObjects(a, b) {\n  if (a && !b || b && !a) {\n    return false;\n  }\n\n  for (var key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n\n  return true;\n} // Copied from: https://github.com/jonschlinkert/is-plain-object\n\nexport function isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  } // If has modified constructor\n\n\n  var ctor = o.constructor;\n\n  if (typeof ctor === 'undefined') {\n    return true;\n  } // If has modified prototype\n\n\n  var prot = ctor.prototype;\n\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  } // If constructor does not have an Object-specific method\n\n\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false;\n  } // Most likely a plain Object\n\n\n  return true;\n}\n\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === '[object Object]';\n}\n\nexport function isQueryKey(value) {\n  return typeof value === 'string' || Array.isArray(value);\n}\nexport function isError(value) {\n  return value instanceof Error;\n}\nexport function sleep(timeout) {\n  return new Promise(function (resolve) {\n    setTimeout(resolve, timeout);\n  });\n}\n/**\n * Schedules a microtask.\n * This can be useful to schedule state updates after rendering.\n */\n\nexport function scheduleMicrotask(callback) {\n  Promise.resolve().then(callback).catch(function (error) {\n    return setTimeout(function () {\n      throw error;\n    });\n  });\n}\nexport function getAbortController() {\n  if (typeof AbortController === 'function') {\n    return new AbortController();\n  }\n}", "import _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var FocusManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(Subscribable);\nexport var focusManager = new FocusManager();", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(Subscribable);\nexport var onlineManager = new OnlineManager();", "import { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { sleep } from './utils';\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1000 * Math.pow(2, failureCount), 30000);\n}\n\nexport function isCancelable(value) {\n  return typeof (value == null ? void 0 : value.cancel) === 'function';\n}\nexport var CancelledError = function CancelledError(options) {\n  this.revert = options == null ? void 0 : options.revert;\n  this.silent = options == null ? void 0 : options.silent;\n};\nexport function isCancelledError(value) {\n  return value instanceof CancelledError;\n} // CLASS\n\nexport var Retryer = function Retryer(config) {\n  var _this = this;\n\n  var cancelRetry = false;\n  var cancelFn;\n  var continueFn;\n  var promiseResolve;\n  var promiseReject;\n  this.abort = config.abort;\n\n  this.cancel = function (cancelOptions) {\n    return cancelFn == null ? void 0 : cancelFn(cancelOptions);\n  };\n\n  this.cancelRetry = function () {\n    cancelRetry = true;\n  };\n\n  this.continueRetry = function () {\n    cancelRetry = false;\n  };\n\n  this.continue = function () {\n    return continueFn == null ? void 0 : continueFn();\n  };\n\n  this.failureCount = 0;\n  this.isPaused = false;\n  this.isResolved = false;\n  this.isTransportCancelable = false;\n  this.promise = new Promise(function (outerResolve, outerReject) {\n    promiseResolve = outerResolve;\n    promiseReject = outerReject;\n  });\n\n  var resolve = function resolve(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onSuccess == null ? void 0 : config.onSuccess(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseResolve(value);\n    }\n  };\n\n  var reject = function reject(value) {\n    if (!_this.isResolved) {\n      _this.isResolved = true;\n      config.onError == null ? void 0 : config.onError(value);\n      continueFn == null ? void 0 : continueFn();\n      promiseReject(value);\n    }\n  };\n\n  var pause = function pause() {\n    return new Promise(function (continueResolve) {\n      continueFn = continueResolve;\n      _this.isPaused = true;\n      config.onPause == null ? void 0 : config.onPause();\n    }).then(function () {\n      continueFn = undefined;\n      _this.isPaused = false;\n      config.onContinue == null ? void 0 : config.onContinue();\n    });\n  }; // Create loop function\n\n\n  var run = function run() {\n    // Do nothing if already resolved\n    if (_this.isResolved) {\n      return;\n    }\n\n    var promiseOrValue; // Execute query\n\n    try {\n      promiseOrValue = config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    } // Create callback to cancel this fetch\n\n\n    cancelFn = function cancelFn(cancelOptions) {\n      if (!_this.isResolved) {\n        reject(new CancelledError(cancelOptions));\n        _this.abort == null ? void 0 : _this.abort(); // Cancel transport if supported\n\n        if (isCancelable(promiseOrValue)) {\n          try {\n            promiseOrValue.cancel();\n          } catch (_unused) {}\n        }\n      }\n    }; // Check if the transport layer support cancellation\n\n\n    _this.isTransportCancelable = isCancelable(promiseOrValue);\n    Promise.resolve(promiseOrValue).then(resolve).catch(function (error) {\n      var _config$retry, _config$retryDelay;\n\n      // Stop if the fetch is already resolved\n      if (_this.isResolved) {\n        return;\n      } // Do we need to retry the request?\n\n\n      var retry = (_config$retry = config.retry) != null ? _config$retry : 3;\n      var retryDelay = (_config$retryDelay = config.retryDelay) != null ? _config$retryDelay : defaultRetryDelay;\n      var delay = typeof retryDelay === 'function' ? retryDelay(_this.failureCount, error) : retryDelay;\n      var shouldRetry = retry === true || typeof retry === 'number' && _this.failureCount < retry || typeof retry === 'function' && retry(_this.failureCount, error);\n\n      if (cancelRetry || !shouldRetry) {\n        // We are done if the query does not need to be retried\n        reject(error);\n        return;\n      }\n\n      _this.failureCount++; // Notify on fail\n\n      config.onFail == null ? void 0 : config.onFail(_this.failureCount, error); // Delay\n\n      sleep(delay) // Pause if the document is not visible or when the device is offline\n      .then(function () {\n        if (!focusManager.isFocused() || !onlineManager.isOnline()) {\n          return pause();\n        }\n      }).then(function () {\n        if (cancelRetry) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  }; // Start loop\n\n\n  run();\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getAbortController, functionalUpdate, isValidTimeout, noop, replaceEqualDeep, timeUntilStale, ensureQueryKeyArray } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { getLogger } from './logger';\nimport { Retryer, isCancelledError } from './retryer'; // TYPES\n\n// CLASS\nexport var Query = /*#__PURE__*/function () {\n  function Query(config) {\n    this.abortSignalConsumed = false;\n    this.hadObservers = false;\n    this.defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.initialState = config.state || this.getDefaultState(this.options);\n    this.state = this.initialState;\n    this.meta = config.meta;\n    this.scheduleGc();\n  }\n\n  var _proto = Query.prototype;\n\n  _proto.setOptions = function setOptions(options) {\n    var _this$options$cacheTi;\n\n    this.options = _extends({}, this.defaultOptions, options);\n    this.meta = options == null ? void 0 : options.meta; // Default to 5 minutes if not cache time is set\n\n    this.cacheTime = Math.max(this.cacheTime || 0, (_this$options$cacheTi = this.options.cacheTime) != null ? _this$options$cacheTi : 5 * 60 * 1000);\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.scheduleGc = function scheduleGc() {\n    var _this = this;\n\n    this.clearGcTimeout();\n\n    if (isValidTimeout(this.cacheTime)) {\n      this.gcTimeout = setTimeout(function () {\n        _this.optionalRemove();\n      }, this.cacheTime);\n    }\n  };\n\n  _proto.clearGcTimeout = function clearGcTimeout() {\n    if (this.gcTimeout) {\n      clearTimeout(this.gcTimeout);\n      this.gcTimeout = undefined;\n    }\n  };\n\n  _proto.optionalRemove = function optionalRemove() {\n    if (!this.observers.length) {\n      if (this.state.isFetching) {\n        if (this.hadObservers) {\n          this.scheduleGc();\n        }\n      } else {\n        this.cache.remove(this);\n      }\n    }\n  };\n\n  _proto.setData = function setData(updater, options) {\n    var _this$options$isDataE, _this$options;\n\n    var prevData = this.state.data; // Get the new data\n\n    var data = functionalUpdate(updater, prevData); // Use prev data if an isDataEqual function is defined and returns `true`\n\n    if ((_this$options$isDataE = (_this$options = this.options).isDataEqual) == null ? void 0 : _this$options$isDataE.call(_this$options, prevData, data)) {\n      data = prevData;\n    } else if (this.options.structuralSharing !== false) {\n      // Structurally share data between prev and new data if needed\n      data = replaceEqualDeep(prevData, data);\n    } // Set data and mark it as cached\n\n\n    this.dispatch({\n      data: data,\n      type: 'success',\n      dataUpdatedAt: options == null ? void 0 : options.updatedAt\n    });\n    return data;\n  };\n\n  _proto.setState = function setState(state, setStateOptions) {\n    this.dispatch({\n      type: 'setState',\n      state: state,\n      setStateOptions: setStateOptions\n    });\n  };\n\n  _proto.cancel = function cancel(options) {\n    var _this$retryer;\n\n    var promise = this.promise;\n    (_this$retryer = this.retryer) == null ? void 0 : _this$retryer.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  };\n\n  _proto.destroy = function destroy() {\n    this.clearGcTimeout();\n    this.cancel({\n      silent: true\n    });\n  };\n\n  _proto.reset = function reset() {\n    this.destroy();\n    this.setState(this.initialState);\n  };\n\n  _proto.isActive = function isActive() {\n    return this.observers.some(function (observer) {\n      return observer.options.enabled !== false;\n    });\n  };\n\n  _proto.isFetching = function isFetching() {\n    return this.state.isFetching;\n  };\n\n  _proto.isStale = function isStale() {\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || this.observers.some(function (observer) {\n      return observer.getCurrentResult().isStale;\n    });\n  };\n\n  _proto.isStaleByTime = function isStaleByTime(staleTime) {\n    if (staleTime === void 0) {\n      staleTime = 0;\n    }\n\n    return this.state.isInvalidated || !this.state.dataUpdatedAt || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this$retryer2;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnWindowFocus();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer2 = this.retryer) == null ? void 0 : _this$retryer2.continue();\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this$retryer3;\n\n    var observer = this.observers.find(function (x) {\n      return x.shouldFetchOnReconnect();\n    });\n\n    if (observer) {\n      observer.refetch();\n    } // Continue fetch if currently paused\n\n\n    (_this$retryer3 = this.retryer) == null ? void 0 : _this$retryer3.continue();\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n      this.hadObservers = true; // Stop the query from being garbage collected\n\n      this.clearGcTimeout();\n      this.cache.notify({\n        type: 'observerAdded',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    if (this.observers.indexOf(observer) !== -1) {\n      this.observers = this.observers.filter(function (x) {\n        return x !== observer;\n      });\n\n      if (!this.observers.length) {\n        // If the transport layer does not support cancellation\n        // we'll let the query continue so the result can be cached\n        if (this.retryer) {\n          if (this.retryer.isTransportCancelable || this.abortSignalConsumed) {\n            this.retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.retryer.cancelRetry();\n          }\n        }\n\n        if (this.cacheTime) {\n          this.scheduleGc();\n        } else {\n          this.cache.remove(this);\n        }\n      }\n\n      this.cache.notify({\n        type: 'observerRemoved',\n        query: this,\n        observer: observer\n      });\n    }\n  };\n\n  _proto.getObserversCount = function getObserversCount() {\n    return this.observers.length;\n  };\n\n  _proto.invalidate = function invalidate() {\n    if (!this.state.isInvalidated) {\n      this.dispatch({\n        type: 'invalidate'\n      });\n    }\n  };\n\n  _proto.fetch = function fetch(options, fetchOptions) {\n    var _this2 = this,\n        _this$options$behavio,\n        _context$fetchOptions,\n        _abortController$abor;\n\n    if (this.state.isFetching) {\n      if (this.state.dataUpdatedAt && (fetchOptions == null ? void 0 : fetchOptions.cancelRefetch)) {\n        // Silently cancel current fetch if the user wants to cancel refetches\n        this.cancel({\n          silent: true\n        });\n      } else if (this.promise) {\n        var _this$retryer4;\n\n        // make sure that retries that were potentially cancelled due to unmounts can continue\n        (_this$retryer4 = this.retryer) == null ? void 0 : _this$retryer4.continueRetry(); // Return current promise if we are already fetching\n\n        return this.promise;\n      }\n    } // Update config if passed, otherwise the config from the last execution is used\n\n\n    if (options) {\n      this.setOptions(options);\n    } // Use the options from the first observer with a query function if no function is found.\n    // This can happen when the query is hydrated or created with setQueryData.\n\n\n    if (!this.options.queryFn) {\n      var observer = this.observers.find(function (x) {\n        return x.options.queryFn;\n      });\n\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n\n    var queryKey = ensureQueryKeyArray(this.queryKey);\n    var abortController = getAbortController(); // Create query function context\n\n    var queryFnContext = {\n      queryKey: queryKey,\n      pageParam: undefined,\n      meta: this.meta\n    };\n    Object.defineProperty(queryFnContext, 'signal', {\n      enumerable: true,\n      get: function get() {\n        if (abortController) {\n          _this2.abortSignalConsumed = true;\n          return abortController.signal;\n        }\n\n        return undefined;\n      }\n    }); // Create fetch function\n\n    var fetchFn = function fetchFn() {\n      if (!_this2.options.queryFn) {\n        return Promise.reject('Missing queryFn');\n      }\n\n      _this2.abortSignalConsumed = false;\n      return _this2.options.queryFn(queryFnContext);\n    }; // Trigger behavior hook\n\n\n    var context = {\n      fetchOptions: fetchOptions,\n      options: this.options,\n      queryKey: queryKey,\n      state: this.state,\n      fetchFn: fetchFn,\n      meta: this.meta\n    };\n\n    if ((_this$options$behavio = this.options.behavior) == null ? void 0 : _this$options$behavio.onFetch) {\n      var _this$options$behavio2;\n\n      (_this$options$behavio2 = this.options.behavior) == null ? void 0 : _this$options$behavio2.onFetch(context);\n    } // Store state in case the current fetch needs to be reverted\n\n\n    this.revertState = this.state; // Set to fetching state if not already in it\n\n    if (!this.state.isFetching || this.state.fetchMeta !== ((_context$fetchOptions = context.fetchOptions) == null ? void 0 : _context$fetchOptions.meta)) {\n      var _context$fetchOptions2;\n\n      this.dispatch({\n        type: 'fetch',\n        meta: (_context$fetchOptions2 = context.fetchOptions) == null ? void 0 : _context$fetchOptions2.meta\n      });\n    } // Try to fetch the data\n\n\n    this.retryer = new Retryer({\n      fn: context.fetchFn,\n      abort: abortController == null ? void 0 : (_abortController$abor = abortController.abort) == null ? void 0 : _abortController$abor.bind(abortController),\n      onSuccess: function onSuccess(data) {\n        _this2.setData(data); // Notify cache callback\n\n\n        _this2.cache.config.onSuccess == null ? void 0 : _this2.cache.config.onSuccess(data, _this2); // Remove query after fetching if cache time is 0\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onError: function onError(error) {\n        // Optimistically update state if needed\n        if (!(isCancelledError(error) && error.silent)) {\n          _this2.dispatch({\n            type: 'error',\n            error: error\n          });\n        }\n\n        if (!isCancelledError(error)) {\n          // Notify cache callback\n          _this2.cache.config.onError == null ? void 0 : _this2.cache.config.onError(error, _this2); // Log error\n\n          getLogger().error(error);\n        } // Remove query after fetching if cache time is 0\n\n\n        if (_this2.cacheTime === 0) {\n          _this2.optionalRemove();\n        }\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay\n    });\n    this.promise = this.retryer.promise;\n    return this.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = this.reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onQueryUpdate(action);\n      });\n\n      _this3.cache.notify({\n        query: _this3,\n        type: 'queryUpdated',\n        action: action\n      });\n    });\n  };\n\n  _proto.getDefaultState = function getDefaultState(options) {\n    var data = typeof options.initialData === 'function' ? options.initialData() : options.initialData;\n    var hasInitialData = typeof options.initialData !== 'undefined';\n    var initialDataUpdatedAt = hasInitialData ? typeof options.initialDataUpdatedAt === 'function' ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n    var hasData = typeof data !== 'undefined';\n    return {\n      data: data,\n      dataUpdateCount: 0,\n      dataUpdatedAt: hasData ? initialDataUpdatedAt != null ? initialDataUpdatedAt : Date.now() : 0,\n      error: null,\n      errorUpdateCount: 0,\n      errorUpdatedAt: 0,\n      fetchFailureCount: 0,\n      fetchMeta: null,\n      isFetching: false,\n      isInvalidated: false,\n      isPaused: false,\n      status: hasData ? 'success' : 'idle'\n    };\n  };\n\n  _proto.reducer = function reducer(state, action) {\n    var _action$meta, _action$dataUpdatedAt;\n\n    switch (action.type) {\n      case 'failed':\n        return _extends({}, state, {\n          fetchFailureCount: state.fetchFailureCount + 1\n        });\n\n      case 'pause':\n        return _extends({}, state, {\n          isPaused: true\n        });\n\n      case 'continue':\n        return _extends({}, state, {\n          isPaused: false\n        });\n\n      case 'fetch':\n        return _extends({}, state, {\n          fetchFailureCount: 0,\n          fetchMeta: (_action$meta = action.meta) != null ? _action$meta : null,\n          isFetching: true,\n          isPaused: false\n        }, !state.dataUpdatedAt && {\n          error: null,\n          status: 'loading'\n        });\n\n      case 'success':\n        return _extends({}, state, {\n          data: action.data,\n          dataUpdateCount: state.dataUpdateCount + 1,\n          dataUpdatedAt: (_action$dataUpdatedAt = action.dataUpdatedAt) != null ? _action$dataUpdatedAt : Date.now(),\n          error: null,\n          fetchFailureCount: 0,\n          isFetching: false,\n          isInvalidated: false,\n          isPaused: false,\n          status: 'success'\n        });\n\n      case 'error':\n        var error = action.error;\n\n        if (isCancelledError(error) && error.revert && this.revertState) {\n          return _extends({}, this.revertState);\n        }\n\n        return _extends({}, state, {\n          error: error,\n          errorUpdateCount: state.errorUpdateCount + 1,\n          errorUpdatedAt: Date.now(),\n          fetchFailureCount: state.fetchFailureCount + 1,\n          isFetching: false,\n          isPaused: false,\n          status: 'error'\n        });\n\n      case 'invalidate':\n        return _extends({}, state, {\n          isInvalidated: true\n        });\n\n      case 'setState':\n        return _extends({}, state, action.state);\n\n      default:\n        return state;\n    }\n  };\n\n  return Query;\n}();", "import { scheduleMicrotask } from './utils'; // TYPES\n\n// CLASS\nexport var NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      scheduleMicrotask(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      scheduleMicrotask(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nexport var notifyManager = new NotifyManager();", "// TYPES\n// FUNCTIONS\nvar logger = console;\nexport function getLogger() {\n  return logger;\n}\nexport function setLogger(newLogger) {\n  logger = newLogger;\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils';\nimport { Query } from './query';\nimport { notifyManager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var QueryCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return matchQuery(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return matchQuery(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(Subscribable);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { hashQuery<PERSON>ey, noop, parseFilterArgs, parseQueryArgs, partialMatch<PERSON>ey, hashQueryKeyByOptions } from './utils';\nimport { QueryCache } from './queryCache';\nimport { MutationCache } from './mutationCache';\nimport { focusManager } from './focusManager';\nimport { onlineManager } from './onlineManager';\nimport { notifyManager } from './notifyManager';\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior';\n// CLASS\nexport var QueryClient = /*#__PURE__*/function () {\n  function QueryClient(config) {\n    if (config === void 0) {\n      config = {};\n    }\n\n    this.queryCache = config.queryCache || new QueryCache();\n    this.mutationCache = config.mutationCache || new MutationCache();\n    this.defaultOptions = config.defaultOptions || {};\n    this.queryDefaults = [];\n    this.mutationDefaults = [];\n  }\n\n  var _proto = QueryClient.prototype;\n\n  _proto.mount = function mount() {\n    var _this = this;\n\n    this.unsubscribeFocus = focusManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onFocus();\n\n        _this.queryCache.onFocus();\n      }\n    });\n    this.unsubscribeOnline = onlineManager.subscribe(function () {\n      if (focusManager.isFocused() && onlineManager.isOnline()) {\n        _this.mutationCache.onOnline();\n\n        _this.queryCache.onOnline();\n      }\n    });\n  };\n\n  _proto.unmount = function unmount() {\n    var _this$unsubscribeFocu, _this$unsubscribeOnli;\n\n    (_this$unsubscribeFocu = this.unsubscribeFocus) == null ? void 0 : _this$unsubscribeFocu.call(this);\n    (_this$unsubscribeOnli = this.unsubscribeOnline) == null ? void 0 : _this$unsubscribeOnli.call(this);\n  };\n\n  _proto.isFetching = function isFetching(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    filters.fetching = true;\n    return this.queryCache.findAll(filters).length;\n  };\n\n  _proto.isMutating = function isMutating(filters) {\n    return this.mutationCache.findAll(_extends({}, filters, {\n      fetching: true\n    })).length;\n  };\n\n  _proto.getQueryData = function getQueryData(queryKey, filters) {\n    var _this$queryCache$find;\n\n    return (_this$queryCache$find = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find.state.data;\n  };\n\n  _proto.getQueriesData = function getQueriesData(queryKeyOrFilters) {\n    return this.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref) {\n      var queryKey = _ref.queryKey,\n          state = _ref.state;\n      var data = state.data;\n      return [queryKey, data];\n    });\n  };\n\n  _proto.setQueryData = function setQueryData(queryKey, updater, options) {\n    var parsedOptions = parseQueryArgs(queryKey);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions);\n    return this.queryCache.build(this, defaultedOptions).setData(updater, options);\n  };\n\n  _proto.setQueriesData = function setQueriesData(queryKeyOrFilters, updater, options) {\n    var _this2 = this;\n\n    return notifyManager.batch(function () {\n      return _this2.getQueryCache().findAll(queryKeyOrFilters).map(function (_ref2) {\n        var queryKey = _ref2.queryKey;\n        return [queryKey, _this2.setQueryData(queryKey, updater, options)];\n      });\n    });\n  };\n\n  _proto.getQueryState = function getQueryState(queryKey, filters) {\n    var _this$queryCache$find2;\n\n    return (_this$queryCache$find2 = this.queryCache.find(queryKey, filters)) == null ? void 0 : _this$queryCache$find2.state;\n  };\n\n  _proto.removeQueries = function removeQueries(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    var queryCache = this.queryCache;\n    notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        queryCache.remove(query);\n      });\n    });\n  };\n\n  _proto.resetQueries = function resetQueries(arg1, arg2, arg3) {\n    var _this3 = this;\n\n    var _parseFilterArgs3 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs3[0],\n        options = _parseFilterArgs3[1];\n\n    var queryCache = this.queryCache;\n\n    var refetchFilters = _extends({}, filters, {\n      active: true\n    });\n\n    return notifyManager.batch(function () {\n      queryCache.findAll(filters).forEach(function (query) {\n        query.reset();\n      });\n      return _this3.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.cancelQueries = function cancelQueries(arg1, arg2, arg3) {\n    var _this4 = this;\n\n    var _parseFilterArgs4 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs4[0],\n        _parseFilterArgs4$ = _parseFilterArgs4[1],\n        cancelOptions = _parseFilterArgs4$ === void 0 ? {} : _parseFilterArgs4$;\n\n    if (typeof cancelOptions.revert === 'undefined') {\n      cancelOptions.revert = true;\n    }\n\n    var promises = notifyManager.batch(function () {\n      return _this4.queryCache.findAll(filters).map(function (query) {\n        return query.cancel(cancelOptions);\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.invalidateQueries = function invalidateQueries(arg1, arg2, arg3) {\n    var _ref3,\n        _filters$refetchActiv,\n        _filters$refetchInact,\n        _this5 = this;\n\n    var _parseFilterArgs5 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs5[0],\n        options = _parseFilterArgs5[1];\n\n    var refetchFilters = _extends({}, filters, {\n      // if filters.refetchActive is not provided and filters.active is explicitly false,\n      // e.g. invalidateQueries({ active: false }), we don't want to refetch active queries\n      active: (_ref3 = (_filters$refetchActiv = filters.refetchActive) != null ? _filters$refetchActiv : filters.active) != null ? _ref3 : true,\n      inactive: (_filters$refetchInact = filters.refetchInactive) != null ? _filters$refetchInact : false\n    });\n\n    return notifyManager.batch(function () {\n      _this5.queryCache.findAll(filters).forEach(function (query) {\n        query.invalidate();\n      });\n\n      return _this5.refetchQueries(refetchFilters, options);\n    });\n  };\n\n  _proto.refetchQueries = function refetchQueries(arg1, arg2, arg3) {\n    var _this6 = this;\n\n    var _parseFilterArgs6 = parseFilterArgs(arg1, arg2, arg3),\n        filters = _parseFilterArgs6[0],\n        options = _parseFilterArgs6[1];\n\n    var promises = notifyManager.batch(function () {\n      return _this6.queryCache.findAll(filters).map(function (query) {\n        return query.fetch(undefined, _extends({}, options, {\n          meta: {\n            refetchPage: filters == null ? void 0 : filters.refetchPage\n          }\n        }));\n      });\n    });\n    var promise = Promise.all(promises).then(noop);\n\n    if (!(options == null ? void 0 : options.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.fetchQuery = function fetchQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    var defaultedOptions = this.defaultQueryOptions(parsedOptions); // https://github.com/tannerlinsley/react-query/issues/652\n\n    if (typeof defaultedOptions.retry === 'undefined') {\n      defaultedOptions.retry = false;\n    }\n\n    var query = this.queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(defaultedOptions.staleTime) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  };\n\n  _proto.prefetchQuery = function prefetchQuery(arg1, arg2, arg3) {\n    return this.fetchQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.fetchInfiniteQuery = function fetchInfiniteQuery(arg1, arg2, arg3) {\n    var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n    parsedOptions.behavior = infiniteQueryBehavior();\n    return this.fetchQuery(parsedOptions);\n  };\n\n  _proto.prefetchInfiniteQuery = function prefetchInfiniteQuery(arg1, arg2, arg3) {\n    return this.fetchInfiniteQuery(arg1, arg2, arg3).then(noop).catch(noop);\n  };\n\n  _proto.cancelMutations = function cancelMutations() {\n    var _this7 = this;\n\n    var promises = notifyManager.batch(function () {\n      return _this7.mutationCache.getAll().map(function (mutation) {\n        return mutation.cancel();\n      });\n    });\n    return Promise.all(promises).then(noop).catch(noop);\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    return this.getMutationCache().resumePausedMutations();\n  };\n\n  _proto.executeMutation = function executeMutation(options) {\n    return this.mutationCache.build(this, options).execute();\n  };\n\n  _proto.getQueryCache = function getQueryCache() {\n    return this.queryCache;\n  };\n\n  _proto.getMutationCache = function getMutationCache() {\n    return this.mutationCache;\n  };\n\n  _proto.getDefaultOptions = function getDefaultOptions() {\n    return this.defaultOptions;\n  };\n\n  _proto.setDefaultOptions = function setDefaultOptions(options) {\n    this.defaultOptions = options;\n  };\n\n  _proto.setQueryDefaults = function setQueryDefaults(queryKey, options) {\n    var result = this.queryDefaults.find(function (x) {\n      return hashQueryKey(queryKey) === hashQueryKey(x.queryKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.queryDefaults.push({\n        queryKey: queryKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getQueryDefaults = function getQueryDefaults(queryKey) {\n    var _this$queryDefaults$f;\n\n    return queryKey ? (_this$queryDefaults$f = this.queryDefaults.find(function (x) {\n      return partialMatchKey(queryKey, x.queryKey);\n    })) == null ? void 0 : _this$queryDefaults$f.defaultOptions : undefined;\n  };\n\n  _proto.setMutationDefaults = function setMutationDefaults(mutationKey, options) {\n    var result = this.mutationDefaults.find(function (x) {\n      return hashQueryKey(mutationKey) === hashQueryKey(x.mutationKey);\n    });\n\n    if (result) {\n      result.defaultOptions = options;\n    } else {\n      this.mutationDefaults.push({\n        mutationKey: mutationKey,\n        defaultOptions: options\n      });\n    }\n  };\n\n  _proto.getMutationDefaults = function getMutationDefaults(mutationKey) {\n    var _this$mutationDefault;\n\n    return mutationKey ? (_this$mutationDefault = this.mutationDefaults.find(function (x) {\n      return partialMatchKey(mutationKey, x.mutationKey);\n    })) == null ? void 0 : _this$mutationDefault.defaultOptions : undefined;\n  };\n\n  _proto.defaultQueryOptions = function defaultQueryOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    var defaultedOptions = _extends({}, this.defaultOptions.queries, this.getQueryDefaults(options == null ? void 0 : options.queryKey), options, {\n      _defaulted: true\n    });\n\n    if (!defaultedOptions.queryHash && defaultedOptions.queryKey) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n\n    return defaultedOptions;\n  };\n\n  _proto.defaultQueryObserverOptions = function defaultQueryObserverOptions(options) {\n    return this.defaultQueryOptions(options);\n  };\n\n  _proto.defaultMutationOptions = function defaultMutationOptions(options) {\n    if (options == null ? void 0 : options._defaulted) {\n      return options;\n    }\n\n    return _extends({}, this.defaultOptions.mutations, this.getMutationDefaults(options == null ? void 0 : options.mutationKey), options, {\n      _defaulted: true\n    });\n  };\n\n  _proto.clear = function clear() {\n    this.queryCache.clear();\n    this.mutationCache.clear();\n  };\n\n  return QueryClient;\n}();", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getLogger } from './logger';\nimport { notify<PERSON><PERSON>ger } from './notifyManager';\nimport { Retryer } from './retryer';\nimport { noop } from './utils'; // TYPES\n\n// CLASS\nexport var Mutation = /*#__PURE__*/function () {\n  function Mutation(config) {\n    this.options = _extends({}, config.defaultOptions, config.options);\n    this.mutationId = config.mutationId;\n    this.mutationCache = config.mutationCache;\n    this.observers = [];\n    this.state = config.state || getDefaultState();\n    this.meta = config.meta;\n  }\n\n  var _proto = Mutation.prototype;\n\n  _proto.setState = function setState(state) {\n    this.dispatch({\n      type: 'setState',\n      state: state\n    });\n  };\n\n  _proto.addObserver = function addObserver(observer) {\n    if (this.observers.indexOf(observer) === -1) {\n      this.observers.push(observer);\n    }\n  };\n\n  _proto.removeObserver = function removeObserver(observer) {\n    this.observers = this.observers.filter(function (x) {\n      return x !== observer;\n    });\n  };\n\n  _proto.cancel = function cancel() {\n    if (this.retryer) {\n      this.retryer.cancel();\n      return this.retryer.promise.then(noop).catch(noop);\n    }\n\n    return Promise.resolve();\n  };\n\n  _proto.continue = function _continue() {\n    if (this.retryer) {\n      this.retryer.continue();\n      return this.retryer.promise;\n    }\n\n    return this.execute();\n  };\n\n  _proto.execute = function execute() {\n    var _this = this;\n\n    var data;\n    var restored = this.state.status === 'loading';\n    var promise = Promise.resolve();\n\n    if (!restored) {\n      this.dispatch({\n        type: 'loading',\n        variables: this.options.variables\n      });\n      promise = promise.then(function () {\n        // Notify cache callback\n        _this.mutationCache.config.onMutate == null ? void 0 : _this.mutationCache.config.onMutate(_this.state.variables, _this);\n      }).then(function () {\n        return _this.options.onMutate == null ? void 0 : _this.options.onMutate(_this.state.variables);\n      }).then(function (context) {\n        if (context !== _this.state.context) {\n          _this.dispatch({\n            type: 'loading',\n            context: context,\n            variables: _this.state.variables\n          });\n        }\n      });\n    }\n\n    return promise.then(function () {\n      return _this.executeMutation();\n    }).then(function (result) {\n      data = result; // Notify cache callback\n\n      _this.mutationCache.config.onSuccess == null ? void 0 : _this.mutationCache.config.onSuccess(data, _this.state.variables, _this.state.context, _this);\n    }).then(function () {\n      return _this.options.onSuccess == null ? void 0 : _this.options.onSuccess(data, _this.state.variables, _this.state.context);\n    }).then(function () {\n      return _this.options.onSettled == null ? void 0 : _this.options.onSettled(data, null, _this.state.variables, _this.state.context);\n    }).then(function () {\n      _this.dispatch({\n        type: 'success',\n        data: data\n      });\n\n      return data;\n    }).catch(function (error) {\n      // Notify cache callback\n      _this.mutationCache.config.onError == null ? void 0 : _this.mutationCache.config.onError(error, _this.state.variables, _this.state.context, _this); // Log error\n\n      getLogger().error(error);\n      return Promise.resolve().then(function () {\n        return _this.options.onError == null ? void 0 : _this.options.onError(error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        return _this.options.onSettled == null ? void 0 : _this.options.onSettled(undefined, error, _this.state.variables, _this.state.context);\n      }).then(function () {\n        _this.dispatch({\n          type: 'error',\n          error: error\n        });\n\n        throw error;\n      });\n    });\n  };\n\n  _proto.executeMutation = function executeMutation() {\n    var _this2 = this,\n        _this$options$retry;\n\n    this.retryer = new Retryer({\n      fn: function fn() {\n        if (!_this2.options.mutationFn) {\n          return Promise.reject('No mutationFn found');\n        }\n\n        return _this2.options.mutationFn(_this2.state.variables);\n      },\n      onFail: function onFail() {\n        _this2.dispatch({\n          type: 'failed'\n        });\n      },\n      onPause: function onPause() {\n        _this2.dispatch({\n          type: 'pause'\n        });\n      },\n      onContinue: function onContinue() {\n        _this2.dispatch({\n          type: 'continue'\n        });\n      },\n      retry: (_this$options$retry = this.options.retry) != null ? _this$options$retry : 0,\n      retryDelay: this.options.retryDelay\n    });\n    return this.retryer.promise;\n  };\n\n  _proto.dispatch = function dispatch(action) {\n    var _this3 = this;\n\n    this.state = reducer(this.state, action);\n    notifyManager.batch(function () {\n      _this3.observers.forEach(function (observer) {\n        observer.onMutationUpdate(action);\n      });\n\n      _this3.mutationCache.notify(_this3);\n    });\n  };\n\n  return Mutation;\n}();\nexport function getDefaultState() {\n  return {\n    context: undefined,\n    data: undefined,\n    error: null,\n    failureCount: 0,\n    isPaused: false,\n    status: 'idle',\n    variables: undefined\n  };\n}\n\nfunction reducer(state, action) {\n  switch (action.type) {\n    case 'failed':\n      return _extends({}, state, {\n        failureCount: state.failureCount + 1\n      });\n\n    case 'pause':\n      return _extends({}, state, {\n        isPaused: true\n      });\n\n    case 'continue':\n      return _extends({}, state, {\n        isPaused: false\n      });\n\n    case 'loading':\n      return _extends({}, state, {\n        context: action.context,\n        data: undefined,\n        error: null,\n        isPaused: false,\n        status: 'loading',\n        variables: action.variables\n      });\n\n    case 'success':\n      return _extends({}, state, {\n        data: action.data,\n        error: null,\n        status: 'success',\n        isPaused: false\n      });\n\n    case 'error':\n      return _extends({}, state, {\n        data: undefined,\n        error: action.error,\n        failureCount: state.failureCount + 1,\n        isPaused: false,\n        status: 'error'\n      });\n\n    case 'setState':\n      return _extends({}, state, action.state);\n\n    default:\n      return state;\n  }\n}", "import _inherits<PERSON><PERSON>e from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { notifyManager } from './notifyManager';\nimport { Mutation } from './mutation';\nimport { matchMutation, noop } from './utils';\nimport { Subscribable } from './subscribable'; // TYPES\n\n// CLASS\nexport var MutationCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationCache, _Subscribable);\n\n  function MutationCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.mutations = [];\n    _this.mutationId = 0;\n    return _this;\n  }\n\n  var _proto = MutationCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.mutationId,\n      options: client.defaultMutationOptions(options),\n      state: state,\n      defaultOptions: options.mutationKey ? client.getMutationDefaults(options.mutationKey) : undefined,\n      meta: options.meta\n    });\n    this.add(mutation);\n    return mutation;\n  };\n\n  _proto.add = function add(mutation) {\n    this.mutations.push(mutation);\n    this.notify(mutation);\n  };\n\n  _proto.remove = function remove(mutation) {\n    this.mutations = this.mutations.filter(function (x) {\n      return x !== mutation;\n    });\n    mutation.cancel();\n    this.notify(mutation);\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.mutations.forEach(function (mutation) {\n        _this2.remove(mutation);\n      });\n    });\n  };\n\n  _proto.getAll = function getAll() {\n    return this.mutations;\n  };\n\n  _proto.find = function find(filters) {\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.mutations.find(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.findAll = function findAll(filters) {\n    return this.mutations.filter(function (mutation) {\n      return matchMutation(filters, mutation);\n    });\n  };\n\n  _proto.notify = function notify(mutation) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(mutation);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.resumePausedMutations();\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.resumePausedMutations();\n  };\n\n  _proto.resumePausedMutations = function resumePausedMutations() {\n    var pausedMutations = this.mutations.filter(function (x) {\n      return x.state.isPaused;\n    });\n    return notifyManager.batch(function () {\n      return pausedMutations.reduce(function (promise, mutation) {\n        return promise.then(function () {\n          return mutation.continue().catch(noop);\n        });\n      }, Promise.resolve());\n    });\n  };\n\n  return MutationCache;\n}(Subscribable);", "import { isCancelable } from './retryer';\nimport { getAbortController } from './utils';\nexport function infiniteQueryBehavior() {\n  return {\n    onFetch: function onFetch(context) {\n      context.fetchFn = function () {\n        var _context$fetchOptions, _context$fetchOptions2, _context$fetchOptions3, _context$fetchOptions4, _context$state$data, _context$state$data2;\n\n        var refetchPage = (_context$fetchOptions = context.fetchOptions) == null ? void 0 : (_context$fetchOptions2 = _context$fetchOptions.meta) == null ? void 0 : _context$fetchOptions2.refetchPage;\n        var fetchMore = (_context$fetchOptions3 = context.fetchOptions) == null ? void 0 : (_context$fetchOptions4 = _context$fetchOptions3.meta) == null ? void 0 : _context$fetchOptions4.fetchMore;\n        var pageParam = fetchMore == null ? void 0 : fetchMore.pageParam;\n        var isFetchingNextPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'forward';\n        var isFetchingPreviousPage = (fetchMore == null ? void 0 : fetchMore.direction) === 'backward';\n        var oldPages = ((_context$state$data = context.state.data) == null ? void 0 : _context$state$data.pages) || [];\n        var oldPageParams = ((_context$state$data2 = context.state.data) == null ? void 0 : _context$state$data2.pageParams) || [];\n        var abortController = getAbortController();\n        var abortSignal = abortController == null ? void 0 : abortController.signal;\n        var newPageParams = oldPageParams;\n        var cancelled = false; // Get query function\n\n        var queryFn = context.options.queryFn || function () {\n          return Promise.reject('Missing queryFn');\n        };\n\n        var buildNewPages = function buildNewPages(pages, param, page, previous) {\n          newPageParams = previous ? [param].concat(newPageParams) : [].concat(newPageParams, [param]);\n          return previous ? [page].concat(pages) : [].concat(pages, [page]);\n        }; // Create function to fetch a page\n\n\n        var fetchPage = function fetchPage(pages, manual, param, previous) {\n          if (cancelled) {\n            return Promise.reject('Cancelled');\n          }\n\n          if (typeof param === 'undefined' && !manual && pages.length) {\n            return Promise.resolve(pages);\n          }\n\n          var queryFnContext = {\n            queryKey: context.queryKey,\n            signal: abortSignal,\n            pageParam: param,\n            meta: context.meta\n          };\n          var queryFnResult = queryFn(queryFnContext);\n          var promise = Promise.resolve(queryFnResult).then(function (page) {\n            return buildNewPages(pages, param, page, previous);\n          });\n\n          if (isCancelable(queryFnResult)) {\n            var promiseAsAny = promise;\n            promiseAsAny.cancel = queryFnResult.cancel;\n          }\n\n          return promise;\n        };\n\n        var promise; // Fetch first page?\n\n        if (!oldPages.length) {\n          promise = fetchPage([]);\n        } // Fetch next page?\n        else if (isFetchingNextPage) {\n            var manual = typeof pageParam !== 'undefined';\n            var param = manual ? pageParam : getNextPageParam(context.options, oldPages);\n            promise = fetchPage(oldPages, manual, param);\n          } // Fetch previous page?\n          else if (isFetchingPreviousPage) {\n              var _manual = typeof pageParam !== 'undefined';\n\n              var _param = _manual ? pageParam : getPreviousPageParam(context.options, oldPages);\n\n              promise = fetchPage(oldPages, _manual, _param, true);\n            } // Refetch pages\n            else {\n                (function () {\n                  newPageParams = [];\n                  var manual = typeof context.options.getNextPageParam === 'undefined';\n                  var shouldFetchFirstPage = refetchPage && oldPages[0] ? refetchPage(oldPages[0], 0, oldPages) : true; // Fetch first page\n\n                  promise = shouldFetchFirstPage ? fetchPage([], manual, oldPageParams[0]) : Promise.resolve(buildNewPages([], oldPageParams[0], oldPages[0])); // Fetch remaining pages\n\n                  var _loop = function _loop(i) {\n                    promise = promise.then(function (pages) {\n                      var shouldFetchNextPage = refetchPage && oldPages[i] ? refetchPage(oldPages[i], i, oldPages) : true;\n\n                      if (shouldFetchNextPage) {\n                        var _param2 = manual ? oldPageParams[i] : getNextPageParam(context.options, pages);\n\n                        return fetchPage(pages, manual, _param2);\n                      }\n\n                      return Promise.resolve(buildNewPages(pages, oldPageParams[i], oldPages[i]));\n                    });\n                  };\n\n                  for (var i = 1; i < oldPages.length; i++) {\n                    _loop(i);\n                  }\n                })();\n              }\n\n        var finalPromise = promise.then(function (pages) {\n          return {\n            pages: pages,\n            pageParams: newPageParams\n          };\n        });\n        var finalPromiseAsAny = finalPromise;\n\n        finalPromiseAsAny.cancel = function () {\n          cancelled = true;\n          abortController == null ? void 0 : abortController.abort();\n\n          if (isCancelable(promise)) {\n            promise.cancel();\n          }\n        };\n\n        return finalPromise;\n      };\n    }\n  };\n}\nexport function getNextPageParam(options, pages) {\n  return options.getNextPageParam == null ? void 0 : options.getNextPageParam(pages[pages.length - 1], pages);\n}\nexport function getPreviousPageParam(options, pages) {\n  return options.getPreviousPageParam == null ? void 0 : options.getPreviousPageParam(pages[0], pages);\n}\n/**\n * Checks if there is a next page.\n * Returns `undefined` if it cannot be determined.\n */\n\nexport function hasNextPage(options, pages) {\n  if (options.getNextPageParam && Array.isArray(pages)) {\n    var nextPageParam = getNextPageParam(options, pages);\n    return typeof nextPageParam !== 'undefined' && nextPageParam !== null && nextPageParam !== false;\n  }\n}\n/**\n * Checks if there is a previous page.\n * Returns `undefined` if it cannot be determined.\n */\n\nexport function hasPreviousPage(options, pages) {\n  if (options.getPreviousPageParam && Array.isArray(pages)) {\n    var previousPageParam = getPreviousPageParam(options, pages);\n    return typeof previousPageParam !== 'undefined' && previousPageParam !== null && previousPageParam !== false;\n  }\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { isServer, isValidTimeout, noop, replaceEqualDeep, shallowEqualObjects, timeUntilStale } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { focusManager } from './focusManager';\nimport { Subscribable } from './subscribable';\nimport { getLogger } from './logger';\nimport { isCancelledError } from './retryer';\nexport var QueryObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryObserver, _Subscribable);\n\n  function QueryObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.options = options;\n    _this.trackedProps = [];\n    _this.selectError = null;\n\n    _this.bindMethods();\n\n    _this.setOptions(options);\n\n    return _this;\n  }\n\n  var _proto = QueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.remove = this.remove.bind(this);\n    this.refetch = this.refetch.bind(this);\n  };\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (this.listeners.length === 1) {\n      this.currentQuery.addObserver(this);\n\n      if (shouldFetchOnMount(this.currentQuery, this.options)) {\n        this.executeFetch();\n      }\n\n      this.updateTimers();\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.shouldFetchOnReconnect = function shouldFetchOnReconnect() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnReconnect);\n  };\n\n  _proto.shouldFetchOnWindowFocus = function shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.currentQuery, this.options, this.options.refetchOnWindowFocus);\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.clearTimers();\n    this.currentQuery.removeObserver(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    var prevOptions = this.options;\n    var prevQuery = this.currentQuery;\n    this.options = this.client.defaultQueryObserverOptions(options);\n\n    if (typeof this.options.enabled !== 'undefined' && typeof this.options.enabled !== 'boolean') {\n      throw new Error('Expected enabled to be a boolean');\n    } // Keep previous query key if the user does not supply one\n\n\n    if (!this.options.queryKey) {\n      this.options.queryKey = prevOptions.queryKey;\n    }\n\n    this.updateQuery();\n    var mounted = this.hasListeners(); // Fetch if there are subscribers\n\n    if (mounted && shouldFetchOptionally(this.currentQuery, prevQuery, this.options, prevOptions)) {\n      this.executeFetch();\n    } // Update result\n\n\n    this.updateResult(notifyOptions); // Update stale interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || this.options.staleTime !== prevOptions.staleTime)) {\n      this.updateStaleTimeout();\n    }\n\n    var nextRefetchInterval = this.computeRefetchInterval(); // Update refetch interval if needed\n\n    if (mounted && (this.currentQuery !== prevQuery || this.options.enabled !== prevOptions.enabled || nextRefetchInterval !== this.currentRefetchInterval)) {\n      this.updateRefetchInterval(nextRefetchInterval);\n    }\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return this.createResult(query, defaultedOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.trackResult = function trackResult(result, defaultedOptions) {\n    var _this2 = this;\n\n    var trackedResult = {};\n\n    var trackProp = function trackProp(key) {\n      if (!_this2.trackedProps.includes(key)) {\n        _this2.trackedProps.push(key);\n      }\n    };\n\n    Object.keys(result).forEach(function (key) {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: function get() {\n          trackProp(key);\n          return result[key];\n        }\n      });\n    });\n\n    if (defaultedOptions.useErrorBoundary || defaultedOptions.suspense) {\n      trackProp('error');\n    }\n\n    return trackedResult;\n  };\n\n  _proto.getNextResult = function getNextResult(options) {\n    var _this3 = this;\n\n    return new Promise(function (resolve, reject) {\n      var unsubscribe = _this3.subscribe(function (result) {\n        if (!result.isFetching) {\n          unsubscribe();\n\n          if (result.isError && (options == null ? void 0 : options.throwOnError)) {\n            reject(result.error);\n          } else {\n            resolve(result);\n          }\n        }\n      });\n    });\n  };\n\n  _proto.getCurrentQuery = function getCurrentQuery() {\n    return this.currentQuery;\n  };\n\n  _proto.remove = function remove() {\n    this.client.getQueryCache().remove(this.currentQuery);\n  };\n\n  _proto.refetch = function refetch(options) {\n    return this.fetch(_extends({}, options, {\n      meta: {\n        refetchPage: options == null ? void 0 : options.refetchPage\n      }\n    }));\n  };\n\n  _proto.fetchOptimistic = function fetchOptimistic(options) {\n    var _this4 = this;\n\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var query = this.client.getQueryCache().build(this.client, defaultedOptions);\n    return query.fetch().then(function () {\n      return _this4.createResult(query, defaultedOptions);\n    });\n  };\n\n  _proto.fetch = function fetch(fetchOptions) {\n    var _this5 = this;\n\n    return this.executeFetch(fetchOptions).then(function () {\n      _this5.updateResult();\n\n      return _this5.currentResult;\n    });\n  };\n\n  _proto.executeFetch = function executeFetch(fetchOptions) {\n    // Make sure we reference the latest query as the current one might have been removed\n    this.updateQuery(); // Fetch\n\n    var promise = this.currentQuery.fetch(this.options, fetchOptions);\n\n    if (!(fetchOptions == null ? void 0 : fetchOptions.throwOnError)) {\n      promise = promise.catch(noop);\n    }\n\n    return promise;\n  };\n\n  _proto.updateStaleTimeout = function updateStaleTimeout() {\n    var _this6 = this;\n\n    this.clearStaleTimeout();\n\n    if (isServer || this.currentResult.isStale || !isValidTimeout(this.options.staleTime)) {\n      return;\n    }\n\n    var time = timeUntilStale(this.currentResult.dataUpdatedAt, this.options.staleTime); // The timeout is sometimes triggered 1 ms before the stale time expiration.\n    // To mitigate this issue we always add 1 ms to the timeout.\n\n    var timeout = time + 1;\n    this.staleTimeoutId = setTimeout(function () {\n      if (!_this6.currentResult.isStale) {\n        _this6.updateResult();\n      }\n    }, timeout);\n  };\n\n  _proto.computeRefetchInterval = function computeRefetchInterval() {\n    var _this$options$refetch;\n\n    return typeof this.options.refetchInterval === 'function' ? this.options.refetchInterval(this.currentResult.data, this.currentQuery) : (_this$options$refetch = this.options.refetchInterval) != null ? _this$options$refetch : false;\n  };\n\n  _proto.updateRefetchInterval = function updateRefetchInterval(nextInterval) {\n    var _this7 = this;\n\n    this.clearRefetchInterval();\n    this.currentRefetchInterval = nextInterval;\n\n    if (isServer || this.options.enabled === false || !isValidTimeout(this.currentRefetchInterval) || this.currentRefetchInterval === 0) {\n      return;\n    }\n\n    this.refetchIntervalId = setInterval(function () {\n      if (_this7.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        _this7.executeFetch();\n      }\n    }, this.currentRefetchInterval);\n  };\n\n  _proto.updateTimers = function updateTimers() {\n    this.updateStaleTimeout();\n    this.updateRefetchInterval(this.computeRefetchInterval());\n  };\n\n  _proto.clearTimers = function clearTimers() {\n    this.clearStaleTimeout();\n    this.clearRefetchInterval();\n  };\n\n  _proto.clearStaleTimeout = function clearStaleTimeout() {\n    if (this.staleTimeoutId) {\n      clearTimeout(this.staleTimeoutId);\n      this.staleTimeoutId = undefined;\n    }\n  };\n\n  _proto.clearRefetchInterval = function clearRefetchInterval() {\n    if (this.refetchIntervalId) {\n      clearInterval(this.refetchIntervalId);\n      this.refetchIntervalId = undefined;\n    }\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var prevQuery = this.currentQuery;\n    var prevOptions = this.options;\n    var prevResult = this.currentResult;\n    var prevResultState = this.currentResultState;\n    var prevResultOptions = this.currentResultOptions;\n    var queryChange = query !== prevQuery;\n    var queryInitialState = queryChange ? query.state : this.currentQueryInitialState;\n    var prevQueryResult = queryChange ? this.currentResult : this.previousQueryResult;\n    var state = query.state;\n    var dataUpdatedAt = state.dataUpdatedAt,\n        error = state.error,\n        errorUpdatedAt = state.errorUpdatedAt,\n        isFetching = state.isFetching,\n        status = state.status;\n    var isPreviousData = false;\n    var isPlaceholderData = false;\n    var data; // Optimistically set result in fetching state if needed\n\n    if (options.optimisticResults) {\n      var mounted = this.hasListeners();\n      var fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      var fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n\n      if (fetchOnMount || fetchOptionally) {\n        isFetching = true;\n\n        if (!dataUpdatedAt) {\n          status = 'loading';\n        }\n      }\n    } // Keep previous data if needed\n\n\n    if (options.keepPreviousData && !state.dataUpdateCount && (prevQueryResult == null ? void 0 : prevQueryResult.isSuccess) && status !== 'error') {\n      data = prevQueryResult.data;\n      dataUpdatedAt = prevQueryResult.dataUpdatedAt;\n      status = prevQueryResult.status;\n      isPreviousData = true;\n    } // Select data if needed\n    else if (options.select && typeof state.data !== 'undefined') {\n        // Memoize select result\n        if (prevResult && state.data === (prevResultState == null ? void 0 : prevResultState.data) && options.select === this.selectFn) {\n          data = this.selectResult;\n        } else {\n          try {\n            this.selectFn = options.select;\n            data = options.select(state.data);\n\n            if (options.structuralSharing !== false) {\n              data = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, data);\n            }\n\n            this.selectResult = data;\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      } // Use query data\n      else {\n          data = state.data;\n        } // Show placeholder data if needed\n\n\n    if (typeof options.placeholderData !== 'undefined' && typeof data === 'undefined' && (status === 'loading' || status === 'idle')) {\n      var placeholderData; // Memoize placeholder data\n\n      if ((prevResult == null ? void 0 : prevResult.isPlaceholderData) && options.placeholderData === (prevResultOptions == null ? void 0 : prevResultOptions.placeholderData)) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === 'function' ? options.placeholderData() : options.placeholderData;\n\n        if (options.select && typeof placeholderData !== 'undefined') {\n          try {\n            placeholderData = options.select(placeholderData);\n\n            if (options.structuralSharing !== false) {\n              placeholderData = replaceEqualDeep(prevResult == null ? void 0 : prevResult.data, placeholderData);\n            }\n\n            this.selectError = null;\n          } catch (selectError) {\n            getLogger().error(selectError);\n            this.selectError = selectError;\n          }\n        }\n      }\n\n      if (typeof placeholderData !== 'undefined') {\n        status = 'success';\n        data = placeholderData;\n        isPlaceholderData = true;\n      }\n    }\n\n    if (this.selectError) {\n      error = this.selectError;\n      data = this.selectResult;\n      errorUpdatedAt = Date.now();\n      status = 'error';\n    }\n\n    var result = {\n      status: status,\n      isLoading: status === 'loading',\n      isSuccess: status === 'success',\n      isError: status === 'error',\n      isIdle: status === 'idle',\n      data: data,\n      dataUpdatedAt: dataUpdatedAt,\n      error: error,\n      errorUpdatedAt: errorUpdatedAt,\n      failureCount: state.fetchFailureCount,\n      errorUpdateCount: state.errorUpdateCount,\n      isFetched: state.dataUpdateCount > 0 || state.errorUpdateCount > 0,\n      isFetchedAfterMount: state.dataUpdateCount > queryInitialState.dataUpdateCount || state.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching: isFetching,\n      isRefetching: isFetching && status !== 'loading',\n      isLoadingError: status === 'error' && state.dataUpdatedAt === 0,\n      isPlaceholderData: isPlaceholderData,\n      isPreviousData: isPreviousData,\n      isRefetchError: status === 'error' && state.dataUpdatedAt !== 0,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      remove: this.remove\n    };\n    return result;\n  };\n\n  _proto.shouldNotifyListeners = function shouldNotifyListeners(result, prevResult) {\n    if (!prevResult) {\n      return true;\n    }\n\n    var _this$options = this.options,\n        notifyOnChangeProps = _this$options.notifyOnChangeProps,\n        notifyOnChangePropsExclusions = _this$options.notifyOnChangePropsExclusions;\n\n    if (!notifyOnChangeProps && !notifyOnChangePropsExclusions) {\n      return true;\n    }\n\n    if (notifyOnChangeProps === 'tracked' && !this.trackedProps.length) {\n      return true;\n    }\n\n    var includedProps = notifyOnChangeProps === 'tracked' ? this.trackedProps : notifyOnChangeProps;\n    return Object.keys(result).some(function (key) {\n      var typedKey = key;\n      var changed = result[typedKey] !== prevResult[typedKey];\n      var isIncluded = includedProps == null ? void 0 : includedProps.some(function (x) {\n        return x === key;\n      });\n      var isExcluded = notifyOnChangePropsExclusions == null ? void 0 : notifyOnChangePropsExclusions.some(function (x) {\n        return x === key;\n      });\n      return changed && !isExcluded && (!includedProps || isIncluded);\n    });\n  };\n\n  _proto.updateResult = function updateResult(notifyOptions) {\n    var prevResult = this.currentResult;\n    this.currentResult = this.createResult(this.currentQuery, this.options);\n    this.currentResultState = this.currentQuery.state;\n    this.currentResultOptions = this.options; // Only notify if something has changed\n\n    if (shallowEqualObjects(this.currentResult, prevResult)) {\n      return;\n    } // Determine which callbacks to trigger\n\n\n    var defaultNotifyOptions = {\n      cache: true\n    };\n\n    if ((notifyOptions == null ? void 0 : notifyOptions.listeners) !== false && this.shouldNotifyListeners(this.currentResult, prevResult)) {\n      defaultNotifyOptions.listeners = true;\n    }\n\n    this.notify(_extends({}, defaultNotifyOptions, notifyOptions));\n  };\n\n  _proto.updateQuery = function updateQuery() {\n    var query = this.client.getQueryCache().build(this.client, this.options);\n\n    if (query === this.currentQuery) {\n      return;\n    }\n\n    var prevQuery = this.currentQuery;\n    this.currentQuery = query;\n    this.currentQueryInitialState = query.state;\n    this.previousQueryResult = this.currentResult;\n\n    if (this.hasListeners()) {\n      prevQuery == null ? void 0 : prevQuery.removeObserver(this);\n      query.addObserver(this);\n    }\n  };\n\n  _proto.onQueryUpdate = function onQueryUpdate(action) {\n    var notifyOptions = {};\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error' && !isCancelledError(action.error)) {\n      notifyOptions.onError = true;\n    }\n\n    this.updateResult(notifyOptions);\n\n    if (this.hasListeners()) {\n      this.updateTimers();\n    }\n  };\n\n  _proto.notify = function notify(notifyOptions) {\n    var _this8 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the configuration callbacks\n      if (notifyOptions.onSuccess) {\n        _this8.options.onSuccess == null ? void 0 : _this8.options.onSuccess(_this8.currentResult.data);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(_this8.currentResult.data, null);\n      } else if (notifyOptions.onError) {\n        _this8.options.onError == null ? void 0 : _this8.options.onError(_this8.currentResult.error);\n        _this8.options.onSettled == null ? void 0 : _this8.options.onSettled(undefined, _this8.currentResult.error);\n      } // Then trigger the listeners\n\n\n      if (notifyOptions.listeners) {\n        _this8.listeners.forEach(function (listener) {\n          listener(_this8.currentResult);\n        });\n      } // Then the cache listeners\n\n\n      if (notifyOptions.cache) {\n        _this8.client.getQueryCache().notify({\n          query: _this8.currentQuery,\n          type: 'observerResultsUpdated'\n        });\n      }\n    });\n  };\n\n  return QueryObserver;\n}(Subscribable);\n\nfunction shouldLoadOnMount(query, options) {\n  return options.enabled !== false && !query.state.dataUpdatedAt && !(query.state.status === 'error' && options.retryOnMount === false);\n}\n\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.dataUpdatedAt > 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\n\nfunction shouldFetchOn(query, options, field) {\n  if (options.enabled !== false) {\n    var value = typeof field === 'function' ? field(query) : field;\n    return value === 'always' || value !== false && isStale(query, options);\n  }\n\n  return false;\n}\n\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return options.enabled !== false && (query !== prevQuery || prevOptions.enabled === false) && (!options.suspense || query.state.status !== 'error') && isStale(query, options);\n}\n\nfunction isStale(query, options) {\n  return query.isStaleByTime(options.staleTime);\n}", "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { difference, replaceAt } from './utils';\nimport { notifyManager } from './notifyManager';\nimport { QueryObserver } from './queryObserver';\nimport { Subscribable } from './subscribable';\nexport var QueriesObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueriesObserver, _Subscribable);\n\n  function QueriesObserver(client, queries) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n    _this.queries = [];\n    _this.result = [];\n    _this.observers = [];\n    _this.observersMap = {};\n\n    if (queries) {\n      _this.setQueries(queries);\n    }\n\n    return _this;\n  }\n\n  var _proto = QueriesObserver.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    var _this2 = this;\n\n    if (this.listeners.length === 1) {\n      this.observers.forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this2.onUpdate(observer, result);\n        });\n      });\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      this.destroy();\n    }\n  };\n\n  _proto.destroy = function destroy() {\n    this.listeners = [];\n    this.observers.forEach(function (observer) {\n      observer.destroy();\n    });\n  };\n\n  _proto.setQueries = function setQueries(queries, notifyOptions) {\n    this.queries = queries;\n    this.updateObservers(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.result;\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(queries) {\n    return this.findMatchingObservers(queries).map(function (match) {\n      return match.observer.getOptimisticResult(match.defaultedQueryOptions);\n    });\n  };\n\n  _proto.findMatchingObservers = function findMatchingObservers(queries) {\n    var _this3 = this;\n\n    var prevObservers = this.observers;\n    var defaultedQueryOptions = queries.map(function (options) {\n      return _this3.client.defaultQueryObserverOptions(options);\n    });\n    var matchingObservers = defaultedQueryOptions.flatMap(function (defaultedOptions) {\n      var match = prevObservers.find(function (observer) {\n        return observer.options.queryHash === defaultedOptions.queryHash;\n      });\n\n      if (match != null) {\n        return [{\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        }];\n      }\n\n      return [];\n    });\n    var matchedQueryHashes = matchingObservers.map(function (match) {\n      return match.defaultedQueryOptions.queryHash;\n    });\n    var unmatchedQueries = defaultedQueryOptions.filter(function (defaultedOptions) {\n      return !matchedQueryHashes.includes(defaultedOptions.queryHash);\n    });\n    var unmatchedObservers = prevObservers.filter(function (prevObserver) {\n      return !matchingObservers.some(function (match) {\n        return match.observer === prevObserver;\n      });\n    });\n    var newOrReusedObservers = unmatchedQueries.map(function (options, index) {\n      if (options.keepPreviousData) {\n        // return previous data from one of the observers that no longer match\n        var previouslyUsedObserver = unmatchedObservers[index];\n\n        if (previouslyUsedObserver !== undefined) {\n          return {\n            defaultedQueryOptions: options,\n            observer: previouslyUsedObserver\n          };\n        }\n      }\n\n      return {\n        defaultedQueryOptions: options,\n        observer: _this3.getObserver(options)\n      };\n    });\n\n    var sortMatchesByOrderOfQueries = function sortMatchesByOrderOfQueries(a, b) {\n      return defaultedQueryOptions.indexOf(a.defaultedQueryOptions) - defaultedQueryOptions.indexOf(b.defaultedQueryOptions);\n    };\n\n    return matchingObservers.concat(newOrReusedObservers).sort(sortMatchesByOrderOfQueries);\n  };\n\n  _proto.getObserver = function getObserver(options) {\n    var defaultedOptions = this.client.defaultQueryObserverOptions(options);\n    var currentObserver = this.observersMap[defaultedOptions.queryHash];\n    return currentObserver != null ? currentObserver : new QueryObserver(this.client, defaultedOptions);\n  };\n\n  _proto.updateObservers = function updateObservers(notifyOptions) {\n    var _this4 = this;\n\n    notifyManager.batch(function () {\n      var prevObservers = _this4.observers;\n\n      var newObserverMatches = _this4.findMatchingObservers(_this4.queries); // set options for the new observers to notify of changes\n\n\n      newObserverMatches.forEach(function (match) {\n        return match.observer.setOptions(match.defaultedQueryOptions, notifyOptions);\n      });\n      var newObservers = newObserverMatches.map(function (match) {\n        return match.observer;\n      });\n      var newObserversMap = Object.fromEntries(newObservers.map(function (observer) {\n        return [observer.options.queryHash, observer];\n      }));\n      var newResult = newObservers.map(function (observer) {\n        return observer.getCurrentResult();\n      });\n      var hasIndexChange = newObservers.some(function (observer, index) {\n        return observer !== prevObservers[index];\n      });\n\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n\n      _this4.observers = newObservers;\n      _this4.observersMap = newObserversMap;\n      _this4.result = newResult;\n\n      if (!_this4.hasListeners()) {\n        return;\n      }\n\n      difference(prevObservers, newObservers).forEach(function (observer) {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(function (observer) {\n        observer.subscribe(function (result) {\n          _this4.onUpdate(observer, result);\n        });\n      });\n\n      _this4.notify();\n    });\n  };\n\n  _proto.onUpdate = function onUpdate(observer, result) {\n    var index = this.observers.indexOf(observer);\n\n    if (index !== -1) {\n      this.result = replaceAt(this.result, index, result);\n      this.notify();\n    }\n  };\n\n  _proto.notify = function notify() {\n    var _this5 = this;\n\n    notifyManager.batch(function () {\n      _this5.listeners.forEach(function (listener) {\n        listener(_this5.result);\n      });\n    });\n  };\n\n  return QueriesObserver;\n}(Subscribable);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { QueryObserver } from './queryObserver';\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from './infiniteQueryBehavior';\nexport var InfiniteQueryObserver = /*#__PURE__*/function (_QueryObserver) {\n  _inheritsLoose(InfiniteQueryObserver, _QueryObserver);\n\n  // Type override\n  // Type override\n  // Type override\n  // eslint-disable-next-line @typescript-eslint/no-useless-constructor\n  function InfiniteQueryObserver(client, options) {\n    return _QueryObserver.call(this, client, options) || this;\n  }\n\n  var _proto = InfiniteQueryObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    _QueryObserver.prototype.bindMethods.call(this);\n\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options, notifyOptions) {\n    _QueryObserver.prototype.setOptions.call(this, _extends({}, options, {\n      behavior: infiniteQueryBehavior()\n    }), notifyOptions);\n  };\n\n  _proto.getOptimisticResult = function getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return _QueryObserver.prototype.getOptimisticResult.call(this, options);\n  };\n\n  _proto.fetchNextPage = function fetchNextPage(options) {\n    var _options$cancelRefetc;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'forward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.fetchPreviousPage = function fetchPreviousPage(options) {\n    var _options$cancelRefetc2;\n\n    return this.fetch({\n      // TODO consider removing `?? true` in future breaking change, to be consistent with `refetch` API (see https://github.com/tannerlinsley/react-query/issues/2617)\n      cancelRefetch: (_options$cancelRefetc2 = options == null ? void 0 : options.cancelRefetch) != null ? _options$cancelRefetc2 : true,\n      throwOnError: options == null ? void 0 : options.throwOnError,\n      meta: {\n        fetchMore: {\n          direction: 'backward',\n          pageParam: options == null ? void 0 : options.pageParam\n        }\n      }\n    });\n  };\n\n  _proto.createResult = function createResult(query, options) {\n    var _state$data, _state$data2, _state$fetchMeta, _state$fetchMeta$fetc, _state$fetchMeta2, _state$fetchMeta2$fet;\n\n    var state = query.state;\n\n    var result = _QueryObserver.prototype.createResult.call(this, query, options);\n\n    return _extends({}, result, {\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, (_state$data = state.data) == null ? void 0 : _state$data.pages),\n      hasPreviousPage: hasPreviousPage(options, (_state$data2 = state.data) == null ? void 0 : _state$data2.pages),\n      isFetchingNextPage: state.isFetching && ((_state$fetchMeta = state.fetchMeta) == null ? void 0 : (_state$fetchMeta$fetc = _state$fetchMeta.fetchMore) == null ? void 0 : _state$fetchMeta$fetc.direction) === 'forward',\n      isFetchingPreviousPage: state.isFetching && ((_state$fetchMeta2 = state.fetchMeta) == null ? void 0 : (_state$fetchMeta2$fet = _state$fetchMeta2.fetchMore) == null ? void 0 : _state$fetchMeta2$fet.direction) === 'backward'\n    });\n  };\n\n  return InfiniteQueryObserver;\n}(QueryObserver);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { getDefaultState } from './mutation';\nimport { notify<PERSON>anager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var MutationObserver = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(MutationObserver, _Subscribable);\n\n  function MutationObserver(client, options) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.client = client;\n\n    _this.setOptions(options);\n\n    _this.bindMethods();\n\n    _this.updateResult();\n\n    return _this;\n  }\n\n  var _proto = MutationObserver.prototype;\n\n  _proto.bindMethods = function bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  };\n\n  _proto.setOptions = function setOptions(options) {\n    this.options = this.client.defaultMutationOptions(options);\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.listeners.length) {\n      var _this$currentMutation;\n\n      (_this$currentMutation = this.currentMutation) == null ? void 0 : _this$currentMutation.removeObserver(this);\n    }\n  };\n\n  _proto.onMutationUpdate = function onMutationUpdate(action) {\n    this.updateResult(); // Determine which callbacks to trigger\n\n    var notifyOptions = {\n      listeners: true\n    };\n\n    if (action.type === 'success') {\n      notifyOptions.onSuccess = true;\n    } else if (action.type === 'error') {\n      notifyOptions.onError = true;\n    }\n\n    this.notify(notifyOptions);\n  };\n\n  _proto.getCurrentResult = function getCurrentResult() {\n    return this.currentResult;\n  };\n\n  _proto.reset = function reset() {\n    this.currentMutation = undefined;\n    this.updateResult();\n    this.notify({\n      listeners: true\n    });\n  };\n\n  _proto.mutate = function mutate(variables, options) {\n    this.mutateOptions = options;\n\n    if (this.currentMutation) {\n      this.currentMutation.removeObserver(this);\n    }\n\n    this.currentMutation = this.client.getMutationCache().build(this.client, _extends({}, this.options, {\n      variables: typeof variables !== 'undefined' ? variables : this.options.variables\n    }));\n    this.currentMutation.addObserver(this);\n    return this.currentMutation.execute();\n  };\n\n  _proto.updateResult = function updateResult() {\n    var state = this.currentMutation ? this.currentMutation.state : getDefaultState();\n\n    var result = _extends({}, state, {\n      isLoading: state.status === 'loading',\n      isSuccess: state.status === 'success',\n      isError: state.status === 'error',\n      isIdle: state.status === 'idle',\n      mutate: this.mutate,\n      reset: this.reset\n    });\n\n    this.currentResult = result;\n  };\n\n  _proto.notify = function notify(options) {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      // First trigger the mutate callbacks\n      if (_this2.mutateOptions) {\n        if (options.onSuccess) {\n          _this2.mutateOptions.onSuccess == null ? void 0 : _this2.mutateOptions.onSuccess(_this2.currentResult.data, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(_this2.currentResult.data, null, _this2.currentResult.variables, _this2.currentResult.context);\n        } else if (options.onError) {\n          _this2.mutateOptions.onError == null ? void 0 : _this2.mutateOptions.onError(_this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n          _this2.mutateOptions.onSettled == null ? void 0 : _this2.mutateOptions.onSettled(undefined, _this2.currentResult.error, _this2.currentResult.variables, _this2.currentResult.context);\n        }\n      } // Then trigger the listeners\n\n\n      if (options.listeners) {\n        _this2.listeners.forEach(function (listener) {\n          listener(_this2.currentResult);\n        });\n      }\n    });\n  };\n\n  return MutationObserver;\n}(Subscribable);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n// TYPES\n// FUNCTIONS\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state\n  };\n} // Most config is not dehydrated but instead meant to configure again when\n// consuming the de/rehydrated data, typically with useQuery on the client.\n// Sometimes it might make sense to prefetch data on the server and include\n// in the html-payload, but not consume it on the initial render.\n\n\nfunction dehydrateQuery(query) {\n  return {\n    state: query.state,\n    queryKey: query.queryKey,\n    queryHash: query.queryHash\n  };\n}\n\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\n\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === 'success';\n}\n\nexport function dehydrate(client, options) {\n  var _options, _options2;\n\n  options = options || {};\n  var mutations = [];\n  var queries = [];\n\n  if (((_options = options) == null ? void 0 : _options.dehydrateMutations) !== false) {\n    var shouldDehydrateMutation = options.shouldDehydrateMutation || defaultShouldDehydrateMutation;\n    client.getMutationCache().getAll().forEach(function (mutation) {\n      if (shouldDehydrateMutation(mutation)) {\n        mutations.push(dehydrateMutation(mutation));\n      }\n    });\n  }\n\n  if (((_options2 = options) == null ? void 0 : _options2.dehydrateQueries) !== false) {\n    var shouldDehydrateQuery = options.shouldDehydrateQuery || defaultShouldDehydrateQuery;\n    client.getQueryCache().getAll().forEach(function (query) {\n      if (shouldDehydrateQuery(query)) {\n        queries.push(dehydrateQuery(query));\n      }\n    });\n  }\n\n  return {\n    mutations: mutations,\n    queries: queries\n  };\n}\nexport function hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== 'object' || dehydratedState === null) {\n    return;\n  }\n\n  var mutationCache = client.getMutationCache();\n  var queryCache = client.getQueryCache();\n  var mutations = dehydratedState.mutations || [];\n  var queries = dehydratedState.queries || [];\n  mutations.forEach(function (dehydratedMutation) {\n    var _options$defaultOptio;\n\n    mutationCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio = options.defaultOptions) == null ? void 0 : _options$defaultOptio.mutations, {\n      mutationKey: dehydratedMutation.mutationKey\n    }), dehydratedMutation.state);\n  });\n  queries.forEach(function (dehydratedQuery) {\n    var _options$defaultOptio2;\n\n    var query = queryCache.get(dehydratedQuery.queryHash); // Do not hydrate if an existing query exists with newer data\n\n    if (query) {\n      if (query.state.dataUpdatedAt < dehydratedQuery.state.dataUpdatedAt) {\n        query.setState(dehydratedQuery.state);\n      }\n\n      return;\n    } // Restore query\n\n\n    queryCache.build(client, _extends({}, options == null ? void 0 : (_options$defaultOptio2 = options.defaultOptions) == null ? void 0 : _options$defaultOptio2.queries, {\n      queryKey: dehydratedQuery.queryKey,\n      queryHash: dehydratedQuery.queryHash\n    }), dehydratedQuery.state);\n  });\n}", "import ReactDOM from 'react-dom';\nexport var unstable_batchedUpdates = ReactDOM.unstable_batchedUpdates;", "import { notifyManager } from '../core';\nimport { unstable_batchedUpdates } from './reactBatchedUpdates';\nnotifyManager.setBatchNotifyFunction(unstable_batchedUpdates);", "export var logger = console;", "import { setLogger } from '../core';\nimport { logger } from './logger';\nsetLogger(logger);", "import React from 'react';\nvar defaultContext = /*#__PURE__*/React.createContext(undefined);\nvar QueryClientSharingContext = /*#__PURE__*/React.createContext(false); // if contextSharing is on, we share the first and at least one\n// instance of the context across the window\n// to ensure that if React Query is used across\n// different bundles or microfrontends they will\n// all use the same **instance** of context, regardless\n// of module scoping.\n\nfunction getQueryClientContext(contextSharing) {\n  if (contextSharing && typeof window !== 'undefined') {\n    if (!window.ReactQueryClientContext) {\n      window.ReactQueryClientContext = defaultContext;\n    }\n\n    return window.ReactQueryClientContext;\n  }\n\n  return defaultContext;\n}\n\nexport var useQueryClient = function useQueryClient() {\n  var queryClient = React.useContext(getQueryClientContext(React.useContext(QueryClientSharingContext)));\n\n  if (!queryClient) {\n    throw new Error('No QueryClient set, use QueryClientProvider to set one');\n  }\n\n  return queryClient;\n};\nexport var QueryClientProvider = function QueryClientProvider(_ref) {\n  var client = _ref.client,\n      _ref$contextSharing = _ref.contextSharing,\n      contextSharing = _ref$contextSharing === void 0 ? false : _ref$contextSharing,\n      children = _ref.children;\n  React.useEffect(function () {\n    client.mount();\n    return function () {\n      client.unmount();\n    };\n  }, [client]);\n  var Context = getQueryClientContext(contextSharing);\n  return /*#__PURE__*/React.createElement(QueryClientSharingContext.Provider, {\n    value: contextSharing\n  }, /*#__PURE__*/React.createElement(Context.Provider, {\n    value: client\n  }, children));\n};", "import React from 'react'; // CONTEXT\n\nfunction createValue() {\n  var _isReset = false;\n  return {\n    clearReset: function clearReset() {\n      _isReset = false;\n    },\n    reset: function reset() {\n      _isReset = true;\n    },\n    isReset: function isReset() {\n      return _isReset;\n    }\n  };\n}\n\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/React.createContext(createValue()); // HOOK\n\nexport var useQueryErrorResetBoundary = function useQueryErrorResetBoundary() {\n  return React.useContext(QueryErrorResetBoundaryContext);\n}; // COMPONENT\n\nexport var QueryErrorResetBoundary = function QueryErrorResetBoundary(_ref) {\n  var children = _ref.children;\n  var value = React.useMemo(function () {\n    return createValue();\n  }, []);\n  return /*#__PURE__*/React.createElement(QueryErrorResetBoundaryContext.Provider, {\n    value: value\n  }, typeof children === 'function' ? children(value) : children);\n};", "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\n\nvar checkIsFetching = function checkIsFetching(queryClient, filters, isFetching, setIsFetching) {\n  var newIsFetching = queryClient.isFetching(filters);\n\n  if (isFetching !== newIsFetching) {\n    setIsFetching(newIsFetching);\n  }\n};\n\nexport function useIsFetching(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var queryClient = useQueryClient();\n\n  var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs[0];\n\n  var _React$useState = React.useState(queryClient.isFetching(filters)),\n      isFetching = _React$useState[0],\n      setIsFetching = _React$useState[1];\n\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isFetchingRef = React.useRef(isFetching);\n  isFetchingRef.current = isFetching;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n    var unsubscribe = queryClient.getQueryCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        checkIsFetching(queryClient, filtersRef.current, isFetchingRef.current, setIsFetching);\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isFetching;\n}", "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { parseMutationFilterArgs } from '../core/utils';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useIsMutating(arg1, arg2) {\n  var mountedRef = React.useRef(false);\n  var filters = parseMutationFilterArgs(arg1, arg2);\n  var queryClient = useQueryClient();\n\n  var _React$useState = React.useState(queryClient.isMutating(filters)),\n      isMutating = _React$useState[0],\n      setIsMutating = _React$useState[1];\n\n  var filtersRef = React.useRef(filters);\n  filtersRef.current = filters;\n  var isMutatingRef = React.useRef(isMutating);\n  isMutatingRef.current = isMutating;\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = queryClient.getMutationCache().subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        var newIsMutating = queryClient.isMutating(filtersRef.current);\n\n        if (isMutatingRef.current !== newIsMutating) {\n          setIsMutating(newIsMutating);\n        }\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [queryClient]);\n  return isMutating;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { noop, parseMutationArgs } from '../core/utils';\nimport { MutationObserver } from '../core/mutationObserver';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils'; // HOOK\n\nexport function useMutation(arg1, arg2, arg3) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var options = parseMutationArgs(arg1, arg2, arg3);\n  var queryClient = useQueryClient();\n  var obsRef = React.useRef();\n\n  if (!obsRef.current) {\n    obsRef.current = new MutationObserver(queryClient, options);\n  } else {\n    obsRef.current.setOptions(options);\n  }\n\n  var currentResult = obsRef.current.getCurrentResult();\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = obsRef.current.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, []);\n  var mutate = React.useCallback(function (variables, mutateOptions) {\n    obsRef.current.mutate(variables, mutateOptions).catch(noop);\n  }, []);\n\n  if (currentResult.error && shouldThrowError(undefined, obsRef.current.options.useErrorBoundary, [currentResult.error])) {\n    throw currentResult.error;\n  }\n\n  return _extends({}, currentResult, {\n    mutate: mutate,\n    mutateAsync: currentResult.mutate\n  });\n}", "export function shouldThrowError(suspense, _useErrorBoundary, params) {\n  // Allow useErrorBoundary function to override throwing behavior on a per-error basis\n  if (typeof _useErrorBoundary === 'function') {\n    return _useErrorBoundary.apply(void 0, params);\n  } // Allow useErrorBoundary to override suspense's throwing behavior\n\n\n  if (typeof _useErrorBoundary === 'boolean') return _useErrorBoundary; // If suspense is enabled default to throwing errors\n\n  return !!suspense;\n}", "import React from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { useQueryErrorResetBoundary } from './QueryErrorResetBoundary';\nimport { useQueryClient } from './QueryClientProvider';\nimport { shouldThrowError } from './utils';\nexport function useBaseQuery(options, Observer) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = useQueryClient();\n  var errorResetBoundary = useQueryErrorResetBoundary();\n  var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure results are optimistically set in fetching state before subscribing or updating options\n\n  defaultedOptions.optimisticResults = true; // Include callbacks in batch renders\n\n  if (defaultedOptions.onError) {\n    defaultedOptions.onError = notifyManager.batchCalls(defaultedOptions.onError);\n  }\n\n  if (defaultedOptions.onSuccess) {\n    defaultedOptions.onSuccess = notifyManager.batchCalls(defaultedOptions.onSuccess);\n  }\n\n  if (defaultedOptions.onSettled) {\n    defaultedOptions.onSettled = notifyManager.batchCalls(defaultedOptions.onSettled);\n  }\n\n  if (defaultedOptions.suspense) {\n    // Always set stale time when using suspense to prevent\n    // fetching again when directly mounting after suspending\n    if (typeof defaultedOptions.staleTime !== 'number') {\n      defaultedOptions.staleTime = 1000;\n    } // Set cache time to 1 if the option has been set to 0\n    // when using suspense to prevent infinite loop of fetches\n\n\n    if (defaultedOptions.cacheTime === 0) {\n      defaultedOptions.cacheTime = 1;\n    }\n  }\n\n  if (defaultedOptions.suspense || defaultedOptions.useErrorBoundary) {\n    // Prevent retrying failed query if the error boundary has not been reset yet\n    if (!errorResetBoundary.isReset()) {\n      defaultedOptions.retryOnMount = false;\n    }\n  }\n\n  var _React$useState2 = React.useState(function () {\n    return new Observer(queryClient, defaultedOptions);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedOptions);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    errorResetBoundary.clearReset();\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    })); // Update result to make sure we did not miss any query updates\n    // between creating the observer and subscribing to it.\n\n    observer.updateResult();\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [errorResetBoundary, observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setOptions(defaultedOptions, {\n      listeners: false\n    });\n  }, [defaultedOptions, observer]); // Handle suspense\n\n  if (defaultedOptions.suspense && result.isLoading) {\n    throw observer.fetchOptimistic(defaultedOptions).then(function (_ref) {\n      var data = _ref.data;\n      defaultedOptions.onSuccess == null ? void 0 : defaultedOptions.onSuccess(data);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(data, null);\n    }).catch(function (error) {\n      errorResetBoundary.clearReset();\n      defaultedOptions.onError == null ? void 0 : defaultedOptions.onError(error);\n      defaultedOptions.onSettled == null ? void 0 : defaultedOptions.onSettled(undefined, error);\n    });\n  } // Handle error boundary\n\n\n  if (result.isError && !errorResetBoundary.isReset() && !result.isFetching && shouldThrowError(defaultedOptions.suspense, defaultedOptions.useErrorBoundary, [result.error, observer.getCurrentQuery()])) {\n    throw result.error;\n  } // Handle result property usage tracking\n\n\n  if (defaultedOptions.notifyOnChangeProps === 'tracked') {\n    result = observer.trackResult(result, defaultedOptions);\n  }\n\n  return result;\n}", "import { QueryObserver } from '../core';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useQuery(arg1, arg2, arg3) {\n  var parsedOptions = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(parsedOptions, QueryObserver);\n}", "import React, { useMemo } from 'react';\nimport { notifyManager } from '../core/notifyManager';\nimport { QueriesObserver } from '../core/queriesObserver';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useQueries(queries) {\n  var mountedRef = React.useRef(false);\n\n  var _React$useState = React.useState(0),\n      forceUpdate = _React$useState[1];\n\n  var queryClient = useQueryClient();\n  var defaultedQueries = useMemo(function () {\n    return queries.map(function (options) {\n      var defaultedOptions = queryClient.defaultQueryObserverOptions(options); // Make sure the results are already in fetching state before subscribing or updating options\n\n      defaultedOptions.optimisticResults = true;\n      return defaultedOptions;\n    });\n  }, [queries, queryClient]);\n\n  var _React$useState2 = React.useState(function () {\n    return new QueriesObserver(queryClient, defaultedQueries);\n  }),\n      observer = _React$useState2[0];\n\n  var result = observer.getOptimisticResult(defaultedQueries);\n  React.useEffect(function () {\n    mountedRef.current = true;\n    var unsubscribe = observer.subscribe(notifyManager.batchCalls(function () {\n      if (mountedRef.current) {\n        forceUpdate(function (x) {\n          return x + 1;\n        });\n      }\n    }));\n    return function () {\n      mountedRef.current = false;\n      unsubscribe();\n    };\n  }, [observer]);\n  React.useEffect(function () {\n    // Do not notify on updates because of changes in the options because\n    // these changes should already be reflected in the optimistic result.\n    observer.setQueries(defaultedQueries, {\n      listeners: false\n    });\n  }, [defaultedQueries, observer]);\n  return result;\n}", "import { InfiniteQueryObserver } from '../core/infiniteQueryObserver';\nimport { parseQueryArgs } from '../core/utils';\nimport { useBaseQuery } from './useBaseQuery'; // HOOK\n\nexport function useInfiniteQuery(arg1, arg2, arg3) {\n  var options = parseQueryArgs(arg1, arg2, arg3);\n  return useBaseQuery(options, InfiniteQueryObserver);\n}", "import React from 'react';\nimport { hydrate } from '../core';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useHydrate(state, options) {\n  var queryClient = useQueryClient();\n  var optionsRef = React.useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  React.useMemo(function () {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nexport var Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n      options = _ref.options,\n      state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAO,IAAI,eAA4B,WAAY;AACjD,WAASA,gBAAe;AACtB,SAAK,YAAY,CAAC;AAAA,EACpB;AAEA,MAAI,SAASA,cAAa;AAE1B,SAAO,YAAY,SAAS,UAAU,UAAU;AAC9C,QAAI,QAAQ;AAEZ,QAAI,WAAW,YAAY,WAAY;AACrC,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,KAAK,QAAQ;AAC5B,SAAK,YAAY;AACjB,WAAO,WAAY;AACjB,YAAM,YAAY,MAAM,UAAU,OAAO,SAAU,GAAG;AACpD,eAAO,MAAM;AAAA,MACf,CAAC;AAED,YAAM,cAAc;AAAA,IACtB;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,eAAe;AAC5C,WAAO,KAAK,UAAU,SAAS;AAAA,EACjC;AAEA,SAAO,cAAc,SAAS,cAAc;AAAA,EAC5C;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAAA,EAChD;AAEA,SAAOA;AACT,EAAE;;;ACpCF;AAGO,IAAI,WAAW,OAAO,WAAW;AACjC,SAAS,OAAO;AACrB,SAAO;AACT;AACO,SAAS,iBAAiB,SAAS,OAAO;AAC/C,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AACO,SAAS,eAAe,OAAO;AACpC,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AACO,SAAS,oBAAoB,OAAO;AACzC,SAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAC9C;AACO,SAAS,WAAW,QAAQ,QAAQ;AACzC,SAAO,OAAO,OAAO,SAAU,GAAG;AAChC,WAAO,OAAO,QAAQ,CAAC,MAAM;AAAA,EAC/B,CAAC;AACH;AACO,SAAS,UAAU,OAAO,OAAO,OAAO;AAC7C,MAAI,OAAO,MAAM,MAAM,CAAC;AACxB,OAAK,KAAK,IAAI;AACd,SAAO;AACT;AACO,SAAS,eAAe,WAAW,WAAW;AACnD,SAAO,KAAK,IAAI,aAAa,aAAa,KAAK,KAAK,IAAI,GAAG,CAAC;AAC9D;AACO,SAAS,eAAe,MAAM,MAAM,MAAM;AAC/C,MAAI,CAAC,WAAW,IAAI,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,SAAS,CAAC,GAAG,MAAM;AAAA,MACxB,UAAU;AAAA,MACV,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,CAAC,GAAG,MAAM;AAAA,IACxB,UAAU;AAAA,EACZ,CAAC;AACH;AACO,SAAS,kBAAkB,MAAM,MAAM,MAAM;AAClD,MAAI,WAAW,IAAI,GAAG;AACpB,QAAI,OAAO,SAAS,YAAY;AAC9B,aAAO,SAAS,CAAC,GAAG,MAAM;AAAA,QACxB,aAAa;AAAA,QACb,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAEA,WAAO,SAAS,CAAC,GAAG,MAAM;AAAA,MACxB,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAEA,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,SAAS,CAAC,GAAG,MAAM;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,CAAC,GAAG,IAAI;AAC1B;AACO,SAAS,gBAAgB,MAAM,MAAM,MAAM;AAChD,SAAO,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM;AAAA,IAC5C,UAAU;AAAA,EACZ,CAAC,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI;AAC/B;AACO,SAAS,wBAAwB,MAAM,MAAM;AAClD,SAAO,WAAW,IAAI,IAAI,SAAS,CAAC,GAAG,MAAM;AAAA,IAC3C,aAAa;AAAA,EACf,CAAC,IAAI;AACP;AACO,SAAS,qBAAqB,QAAQ,UAAU;AACrD,MAAI,WAAW,QAAQ,aAAa,QAAQ,UAAU,QAAQ,YAAY,MAAM;AAC9E,WAAO;AAAA,EACT,WAAW,WAAW,SAAS,aAAa,OAAO;AACjD,WAAO;AAAA,EACT,OAAO;AAGL,QAAI,WAAW,UAAU,OAAO,SAAS,CAAC;AAC1C,WAAO,WAAW,WAAW;AAAA,EAC/B;AACF;AACO,SAAS,WAAW,SAAS,OAAO;AACzC,MAAI,SAAS,QAAQ,QACjB,QAAQ,QAAQ,OAChB,WAAW,QAAQ,UACnB,WAAW,QAAQ,UACnB,YAAY,QAAQ,WACpB,WAAW,QAAQ,UACnB,QAAQ,QAAQ;AAEpB,MAAI,WAAW,QAAQ,GAAG;AACxB,QAAI,OAAO;AACT,UAAI,MAAM,cAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;AACtE,eAAO;AAAA,MACT;AAAA,IACF,WAAW,CAAC,gBAAgB,MAAM,UAAU,QAAQ,GAAG;AACrD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,oBAAoB,qBAAqB,QAAQ,QAAQ;AAE7D,MAAI,sBAAsB,QAAQ;AAChC,WAAO;AAAA,EACT,WAAW,sBAAsB,OAAO;AACtC,QAAI,WAAW,MAAM,SAAS;AAE9B,QAAI,sBAAsB,YAAY,CAAC,UAAU;AAC/C,aAAO;AAAA,IACT;AAEA,QAAI,sBAAsB,cAAc,UAAU;AAChD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,UAAU,aAAa,MAAM,QAAQ,MAAM,OAAO;AAC3D,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,aAAa,aAAa,MAAM,WAAW,MAAM,UAAU;AACpE,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC,UAAU,KAAK,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AACO,SAAS,cAAc,SAAS,UAAU;AAC/C,MAAI,QAAQ,QAAQ,OAChB,WAAW,QAAQ,UACnB,YAAY,QAAQ,WACpB,cAAc,QAAQ;AAE1B,MAAI,WAAW,WAAW,GAAG;AAC3B,QAAI,CAAC,SAAS,QAAQ,aAAa;AACjC,aAAO;AAAA,IACT;AAEA,QAAI,OAAO;AACT,UAAI,aAAa,SAAS,QAAQ,WAAW,MAAM,aAAa,WAAW,GAAG;AAC5E,eAAO;AAAA,MACT;AAAA,IACF,WAAW,CAAC,gBAAgB,SAAS,QAAQ,aAAa,WAAW,GAAG;AACtE,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,OAAO,aAAa,aAAa,SAAS,MAAM,WAAW,cAAc,UAAU;AACrF,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;AACrC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AACO,SAAS,sBAAsB,UAAU,SAAS;AACvD,MAAI,UAAU,WAAW,OAAO,SAAS,QAAQ,mBAAmB;AACpE,SAAO,OAAO,QAAQ;AACxB;AAKO,SAAS,aAAa,UAAU;AACrC,MAAI,UAAU,oBAAoB,QAAQ;AAC1C,SAAO,gBAAgB,OAAO;AAChC;AAKO,SAAS,gBAAgB,OAAO;AACrC,SAAO,KAAK,UAAU,OAAO,SAAU,GAAG,KAAK;AAC7C,WAAO,cAAc,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,SAAU,QAAQ,KAAK;AAChF,aAAO,GAAG,IAAI,IAAI,GAAG;AACrB,aAAO;AAAA,IACT,GAAG,CAAC,CAAC,IAAI;AAAA,EACX,CAAC;AACH;AAKO,SAAS,gBAAgB,GAAG,GAAG;AACpC,SAAO,iBAAiB,oBAAoB,CAAC,GAAG,oBAAoB,CAAC,CAAC;AACxE;AAKO,SAAS,iBAAiB,GAAG,GAAG;AACrC,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,WAAO,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,SAAU,KAAK;AACzC,aAAO,CAAC,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,IACzC,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAOO,SAAS,iBAAiB,GAAG,GAAG;AACrC,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC;AAE/C,MAAI,SAAS,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACjD,QAAI,QAAQ,QAAQ,EAAE,SAAS,OAAO,KAAK,CAAC,EAAE;AAC9C,QAAI,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACtC,QAAI,QAAQ,OAAO;AACnB,QAAI,OAAO,QAAQ,CAAC,IAAI,CAAC;AACzB,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,UAAI,MAAM,QAAQ,IAAI,OAAO,CAAC;AAC9B,WAAK,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAE3C,UAAI,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG;AACxB;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;AAAA,EACvD;AAEA,SAAO;AACT;AAKO,SAAS,oBAAoB,GAAG,GAAG;AACxC,MAAI,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,OAAO,GAAG;AACjB,QAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAEO,SAAS,cAAc,GAAG;AAC/B,MAAI,CAAC,mBAAmB,CAAC,GAAG;AAC1B,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,EAAE;AAEb,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,KAAK;AAEhB,MAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AACzC,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAG;AAC7B,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,WAAW,OAAO;AAChC,SAAO,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK;AACzD;AACO,SAAS,QAAQ,OAAO;AAC7B,SAAO,iBAAiB;AAC1B;AACO,SAAS,MAAM,SAAS;AAC7B,SAAO,IAAI,QAAQ,SAAU,SAAS;AACpC,eAAW,SAAS,OAAO;AAAA,EAC7B,CAAC;AACH;AAMO,SAAS,kBAAkB,UAAU;AAC1C,UAAQ,QAAQ,EAAE,KAAK,QAAQ,EAAE,MAAM,SAAU,OAAO;AACtD,WAAO,WAAW,WAAY;AAC5B,YAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH;AACO,SAAS,qBAAqB;AACnC,MAAI,OAAO,oBAAoB,YAAY;AACzC,WAAO,IAAI,gBAAgB;AAAA,EAC7B;AACF;;;ACxUO,IAAI,eAA4B,SAAU,eAAe;AAC9D,iBAAeC,eAAc,aAAa;AAE1C,WAASA,gBAAe;AACtB,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AAEpC,UAAM,QAAQ,SAAU,SAAS;AAC/B,UAAI;AAEJ,UAAI,CAAC,cAAc,UAAU,WAAW,OAAO,SAAS,QAAQ,mBAAmB;AACjF,YAAI,WAAW,SAASC,YAAW;AACjC,iBAAO,QAAQ;AAAA,QACjB;AAGA,eAAO,iBAAiB,oBAAoB,UAAU,KAAK;AAC3D,eAAO,iBAAiB,SAAS,UAAU,KAAK;AAChD,eAAO,WAAY;AAEjB,iBAAO,oBAAoB,oBAAoB,QAAQ;AACvD,iBAAO,oBAAoB,SAAS,QAAQ;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASD,cAAa;AAE1B,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,iBAAiB,KAAK,KAAK;AAAA,IAClC;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,UAAI;AAEJ,OAAC,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc,KAAK,IAAI;AACzE,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,mBAAmB,SAAS,iBAAiB,OAAO;AACzD,QAAI,gBACA,SAAS;AAEb,SAAK,QAAQ;AACb,KAAC,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,KAAK,IAAI;AAC3E,SAAK,UAAU,MAAM,SAAU,SAAS;AACtC,UAAI,OAAO,YAAY,WAAW;AAChC,eAAO,WAAW,OAAO;AAAA,MAC3B,OAAO;AACL,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS;AAC/C,SAAK,UAAU;AAEf,QAAI,SAAS;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,SAAK,UAAU,QAAQ,SAAU,UAAU;AACzC,eAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO,YAAY,SAAS,YAAY;AACtC,QAAI,OAAO,KAAK,YAAY,WAAW;AACrC,aAAO,KAAK;AAAA,IACd;AAGA,QAAI,OAAO,aAAa,aAAa;AACnC,aAAO;AAAA,IACT;AAEA,WAAO,CAAC,QAAW,WAAW,WAAW,EAAE,SAAS,SAAS,eAAe;AAAA,EAC9E;AAEA,SAAOA;AACT,EAAE,YAAY;AACP,IAAI,eAAe,IAAI,aAAa;;;AC3FpC,IAAI,gBAA6B,SAAU,eAAe;AAC/D,iBAAeE,gBAAe,aAAa;AAE3C,WAASA,iBAAgB;AACvB,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AAEpC,UAAM,QAAQ,SAAU,UAAU;AAChC,UAAI;AAEJ,UAAI,CAAC,cAAc,UAAU,WAAW,OAAO,SAAS,QAAQ,mBAAmB;AACjF,YAAI,WAAW,SAASC,YAAW;AACjC,iBAAO,SAAS;AAAA,QAClB;AAGA,eAAO,iBAAiB,UAAU,UAAU,KAAK;AACjD,eAAO,iBAAiB,WAAW,UAAU,KAAK;AAClD,eAAO,WAAY;AAEjB,iBAAO,oBAAoB,UAAU,QAAQ;AAC7C,iBAAO,oBAAoB,WAAW,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASD,eAAc;AAE3B,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,iBAAiB,KAAK,KAAK;AAAA,IAClC;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,UAAI;AAEJ,OAAC,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc,KAAK,IAAI;AACzE,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,mBAAmB,SAAS,iBAAiB,OAAO;AACzD,QAAI,gBACA,SAAS;AAEb,SAAK,QAAQ;AACb,KAAC,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,KAAK,IAAI;AAC3E,SAAK,UAAU,MAAM,SAAU,QAAQ;AACrC,UAAI,OAAO,WAAW,WAAW;AAC/B,eAAO,UAAU,MAAM;AAAA,MACzB,OAAO;AACL,eAAO,SAAS;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,YAAY,SAAS,UAAU,QAAQ;AAC5C,SAAK,SAAS;AAEd,QAAI,QAAQ;AACV,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,SAAK,UAAU,QAAQ,SAAU,UAAU;AACzC,eAAS;AAAA,IACX,CAAC;AAAA,EACH;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,QAAI,OAAO,KAAK,WAAW,WAAW;AACpC,aAAO,KAAK;AAAA,IACd;AAEA,QAAI,OAAO,cAAc,eAAe,OAAO,UAAU,WAAW,aAAa;AAC/E,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,EACnB;AAEA,SAAOA;AACT,EAAE,YAAY;AACP,IAAI,gBAAgB,IAAI,cAAc;;;ACzF7C,SAAS,kBAAkB,cAAc;AACvC,SAAO,KAAK,IAAI,MAAO,KAAK,IAAI,GAAG,YAAY,GAAG,GAAK;AACzD;AAEO,SAAS,aAAa,OAAO;AAClC,SAAO,QAAQ,SAAS,OAAO,SAAS,MAAM,YAAY;AAC5D;AACO,IAAI,iBAAiB,SAASE,gBAAe,SAAS;AAC3D,OAAK,SAAS,WAAW,OAAO,SAAS,QAAQ;AACjD,OAAK,SAAS,WAAW,OAAO,SAAS,QAAQ;AACnD;AACO,SAAS,iBAAiB,OAAO;AACtC,SAAO,iBAAiB;AAC1B;AAEO,IAAI,UAAU,SAASC,SAAQ,QAAQ;AAC5C,MAAI,QAAQ;AAEZ,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,OAAK,QAAQ,OAAO;AAEpB,OAAK,SAAS,SAAU,eAAe;AACrC,WAAO,YAAY,OAAO,SAAS,SAAS,aAAa;AAAA,EAC3D;AAEA,OAAK,cAAc,WAAY;AAC7B,kBAAc;AAAA,EAChB;AAEA,OAAK,gBAAgB,WAAY;AAC/B,kBAAc;AAAA,EAChB;AAEA,OAAK,WAAW,WAAY;AAC1B,WAAO,cAAc,OAAO,SAAS,WAAW;AAAA,EAClD;AAEA,OAAK,eAAe;AACpB,OAAK,WAAW;AAChB,OAAK,aAAa;AAClB,OAAK,wBAAwB;AAC7B,OAAK,UAAU,IAAI,QAAQ,SAAU,cAAc,aAAa;AAC9D,qBAAiB;AACjB,oBAAgB;AAAA,EAClB,CAAC;AAED,MAAI,UAAU,SAASC,SAAQ,OAAO;AACpC,QAAI,CAAC,MAAM,YAAY;AACrB,YAAM,aAAa;AACnB,aAAO,aAAa,OAAO,SAAS,OAAO,UAAU,KAAK;AAC1D,oBAAc,OAAO,SAAS,WAAW;AACzC,qBAAe,KAAK;AAAA,IACtB;AAAA,EACF;AAEA,MAAI,SAAS,SAASC,QAAO,OAAO;AAClC,QAAI,CAAC,MAAM,YAAY;AACrB,YAAM,aAAa;AACnB,aAAO,WAAW,OAAO,SAAS,OAAO,QAAQ,KAAK;AACtD,oBAAc,OAAO,SAAS,WAAW;AACzC,oBAAc,KAAK;AAAA,IACrB;AAAA,EACF;AAEA,MAAI,QAAQ,SAASC,SAAQ;AAC3B,WAAO,IAAI,QAAQ,SAAU,iBAAiB;AAC5C,mBAAa;AACb,YAAM,WAAW;AACjB,aAAO,WAAW,OAAO,SAAS,OAAO,QAAQ;AAAA,IACnD,CAAC,EAAE,KAAK,WAAY;AAClB,mBAAa;AACb,YAAM,WAAW;AACjB,aAAO,cAAc,OAAO,SAAS,OAAO,WAAW;AAAA,IACzD,CAAC;AAAA,EACH;AAGA,MAAI,MAAM,SAASC,OAAM;AAEvB,QAAI,MAAM,YAAY;AACpB;AAAA,IACF;AAEA,QAAI;AAEJ,QAAI;AACF,uBAAiB,OAAO,GAAG;AAAA,IAC7B,SAAS,OAAO;AACd,uBAAiB,QAAQ,OAAO,KAAK;AAAA,IACvC;AAGA,eAAW,SAASC,UAAS,eAAe;AAC1C,UAAI,CAAC,MAAM,YAAY;AACrB,eAAO,IAAI,eAAe,aAAa,CAAC;AACxC,cAAM,SAAS,OAAO,SAAS,MAAM,MAAM;AAE3C,YAAI,aAAa,cAAc,GAAG;AAChC,cAAI;AACF,2BAAe,OAAO;AAAA,UACxB,SAAS,SAAS;AAAA,UAAC;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AAGA,UAAM,wBAAwB,aAAa,cAAc;AACzD,YAAQ,QAAQ,cAAc,EAAE,KAAK,OAAO,EAAE,MAAM,SAAU,OAAO;AACnE,UAAI,eAAe;AAGnB,UAAI,MAAM,YAAY;AACpB;AAAA,MACF;AAGA,UAAI,SAAS,gBAAgB,OAAO,UAAU,OAAO,gBAAgB;AACrE,UAAI,cAAc,qBAAqB,OAAO,eAAe,OAAO,qBAAqB;AACzF,UAAI,QAAQ,OAAO,eAAe,aAAa,WAAW,MAAM,cAAc,KAAK,IAAI;AACvF,UAAI,cAAc,UAAU,QAAQ,OAAO,UAAU,YAAY,MAAM,eAAe,SAAS,OAAO,UAAU,cAAc,MAAM,MAAM,cAAc,KAAK;AAE7J,UAAI,eAAe,CAAC,aAAa;AAE/B,eAAO,KAAK;AACZ;AAAA,MACF;AAEA,YAAM;AAEN,aAAO,UAAU,OAAO,SAAS,OAAO,OAAO,MAAM,cAAc,KAAK;AAExE,YAAM,KAAK,EACV,KAAK,WAAY;AAChB,YAAI,CAAC,aAAa,UAAU,KAAK,CAAC,cAAc,SAAS,GAAG;AAC1D,iBAAO,MAAM;AAAA,QACf;AAAA,MACF,CAAC,EAAE,KAAK,WAAY;AAClB,YAAI,aAAa;AACf,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,UAAAD,KAAI;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAGA,MAAI;AACN;;;AC5JA;;;ACGO,IAAI,gBAA6B,WAAY;AAClD,WAASE,iBAAgB;AACvB,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe;AAEpB,SAAK,WAAW,SAAU,UAAU;AAClC,eAAS;AAAA,IACX;AAEA,SAAK,gBAAgB,SAAU,UAAU;AACvC,eAAS;AAAA,IACX;AAAA,EACF;AAEA,MAAI,SAASA,eAAc;AAE3B,SAAO,QAAQ,SAAS,MAAM,UAAU;AACtC,QAAI;AACJ,SAAK;AAEL,QAAI;AACF,eAAS,SAAS;AAAA,IACpB,UAAE;AACA,WAAK;AAEL,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,WAAW,SAAS,SAAS,UAAU;AAC5C,QAAI,QAAQ;AAEZ,QAAI,KAAK,cAAc;AACrB,WAAK,MAAM,KAAK,QAAQ;AAAA,IAC1B,OAAO;AACL,wBAAkB,WAAY;AAC5B,cAAM,SAAS,QAAQ;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAMA,SAAO,aAAa,SAAS,WAAW,UAAU;AAChD,QAAI,SAAS;AAEb,WAAO,WAAY;AACjB,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AAEA,aAAO,SAAS,WAAY;AAC1B,iBAAS,MAAM,QAAQ,IAAI;AAAA,MAC7B,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,QAAI,SAAS;AAEb,QAAI,QAAQ,KAAK;AACjB,SAAK,QAAQ,CAAC;AAEd,QAAI,MAAM,QAAQ;AAChB,wBAAkB,WAAY;AAC5B,eAAO,cAAc,WAAY;AAC/B,gBAAM,QAAQ,SAAU,UAAU;AAChC,mBAAO,SAAS,QAAQ;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAOA,SAAO,oBAAoB,SAAS,kBAAkB,IAAI;AACxD,SAAK,WAAW;AAAA,EAClB;AAOA,SAAO,yBAAyB,SAAS,uBAAuB,IAAI;AAClE,SAAK,gBAAgB;AAAA,EACvB;AAEA,SAAOA;AACT,EAAE;AAEK,IAAI,gBAAgB,IAAI,cAAc;;;ACtG7C,IAAI,SAAS;AACN,SAAS,YAAY;AAC1B,SAAO;AACT;AACO,SAAS,UAAU,WAAW;AACnC,WAAS;AACX;;;AFDO,IAAI,QAAqB,WAAY;AAC1C,WAASC,OAAM,QAAQ;AACrB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,iBAAiB,OAAO;AAC7B,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,YAAY,CAAC;AAClB,SAAK,QAAQ,OAAO;AACpB,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY,OAAO;AACxB,SAAK,eAAe,OAAO,SAAS,KAAK,gBAAgB,KAAK,OAAO;AACrE,SAAK,QAAQ,KAAK;AAClB,SAAK,OAAO,OAAO;AACnB,SAAK,WAAW;AAAA,EAClB;AAEA,MAAI,SAASA,OAAM;AAEnB,SAAO,aAAa,SAAS,WAAW,SAAS;AAC/C,QAAI;AAEJ,SAAK,UAAU,SAAS,CAAC,GAAG,KAAK,gBAAgB,OAAO;AACxD,SAAK,OAAO,WAAW,OAAO,SAAS,QAAQ;AAE/C,SAAK,YAAY,KAAK,IAAI,KAAK,aAAa,IAAI,wBAAwB,KAAK,QAAQ,cAAc,OAAO,wBAAwB,IAAI,KAAK,GAAI;AAAA,EACjJ;AAEA,SAAO,oBAAoB,SAAS,kBAAkB,SAAS;AAC7D,SAAK,iBAAiB;AAAA,EACxB;AAEA,SAAO,aAAa,SAAS,aAAa;AACxC,QAAI,QAAQ;AAEZ,SAAK,eAAe;AAEpB,QAAI,eAAe,KAAK,SAAS,GAAG;AAClC,WAAK,YAAY,WAAW,WAAY;AACtC,cAAM,eAAe;AAAA,MACvB,GAAG,KAAK,SAAS;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,iBAAiB,SAAS,iBAAiB;AAChD,QAAI,KAAK,WAAW;AAClB,mBAAa,KAAK,SAAS;AAC3B,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,iBAAiB,SAAS,iBAAiB;AAChD,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,UAAI,KAAK,MAAM,YAAY;AACzB,YAAI,KAAK,cAAc;AACrB,eAAK,WAAW;AAAA,QAClB;AAAA,MACF,OAAO;AACL,aAAK,MAAM,OAAO,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAEA,SAAO,UAAU,SAAS,QAAQ,SAAS,SAAS;AAClD,QAAI,uBAAuB;AAE3B,QAAI,WAAW,KAAK,MAAM;AAE1B,QAAI,OAAO,iBAAiB,SAAS,QAAQ;AAE7C,SAAK,yBAAyB,gBAAgB,KAAK,SAAS,gBAAgB,OAAO,SAAS,sBAAsB,KAAK,eAAe,UAAU,IAAI,GAAG;AACrJ,aAAO;AAAA,IACT,WAAW,KAAK,QAAQ,sBAAsB,OAAO;AAEnD,aAAO,iBAAiB,UAAU,IAAI;AAAA,IACxC;AAGA,SAAK,SAAS;AAAA,MACZ;AAAA,MACA,MAAM;AAAA,MACN,eAAe,WAAW,OAAO,SAAS,QAAQ;AAAA,IACpD,CAAC;AACD,WAAO;AAAA,EACT;AAEA,SAAO,WAAW,SAAS,SAAS,OAAO,iBAAiB;AAC1D,SAAK,SAAS;AAAA,MACZ,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,SAAS,OAAO,SAAS;AACvC,QAAI;AAEJ,QAAI,UAAU,KAAK;AACnB,KAAC,gBAAgB,KAAK,YAAY,OAAO,SAAS,cAAc,OAAO,OAAO;AAC9E,WAAO,UAAU,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,QAAQ,QAAQ;AAAA,EACpE;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,SAAK,eAAe;AACpB,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,SAAK,QAAQ;AACb,SAAK,SAAS,KAAK,YAAY;AAAA,EACjC;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,WAAO,KAAK,UAAU,KAAK,SAAU,UAAU;AAC7C,aAAO,SAAS,QAAQ,YAAY;AAAA,IACtC,CAAC;AAAA,EACH;AAEA,SAAO,aAAa,SAAS,aAAa;AACxC,WAAO,KAAK,MAAM;AAAA,EACpB;AAEA,SAAO,UAAU,SAASC,WAAU;AAClC,WAAO,KAAK,MAAM,iBAAiB,CAAC,KAAK,MAAM,iBAAiB,KAAK,UAAU,KAAK,SAAU,UAAU;AACtG,aAAO,SAAS,iBAAiB,EAAE;AAAA,IACrC,CAAC;AAAA,EACH;AAEA,SAAO,gBAAgB,SAAS,cAAc,WAAW;AACvD,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AAEA,WAAO,KAAK,MAAM,iBAAiB,CAAC,KAAK,MAAM,iBAAiB,CAAC,eAAe,KAAK,MAAM,eAAe,SAAS;AAAA,EACrH;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI;AAEJ,QAAI,WAAW,KAAK,UAAU,KAAK,SAAU,GAAG;AAC9C,aAAO,EAAE,yBAAyB;AAAA,IACpC,CAAC;AAED,QAAI,UAAU;AACZ,eAAS,QAAQ;AAAA,IACnB;AAGA,KAAC,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS;AAAA,EAC7E;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,QAAI;AAEJ,QAAI,WAAW,KAAK,UAAU,KAAK,SAAU,GAAG;AAC9C,aAAO,EAAE,uBAAuB;AAAA,IAClC,CAAC;AAED,QAAI,UAAU;AACZ,eAAS,QAAQ;AAAA,IACnB;AAGA,KAAC,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,SAAS;AAAA,EAC7E;AAEA,SAAO,cAAc,SAAS,YAAY,UAAU;AAClD,QAAI,KAAK,UAAU,QAAQ,QAAQ,MAAM,IAAI;AAC3C,WAAK,UAAU,KAAK,QAAQ;AAC5B,WAAK,eAAe;AAEpB,WAAK,eAAe;AACpB,WAAK,MAAM,OAAO;AAAA,QAChB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,iBAAiB,SAAS,eAAe,UAAU;AACxD,QAAI,KAAK,UAAU,QAAQ,QAAQ,MAAM,IAAI;AAC3C,WAAK,YAAY,KAAK,UAAU,OAAO,SAAU,GAAG;AAClD,eAAO,MAAM;AAAA,MACf,CAAC;AAED,UAAI,CAAC,KAAK,UAAU,QAAQ;AAG1B,YAAI,KAAK,SAAS;AAChB,cAAI,KAAK,QAAQ,yBAAyB,KAAK,qBAAqB;AAClE,iBAAK,QAAQ,OAAO;AAAA,cAClB,QAAQ;AAAA,YACV,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,QAAQ,YAAY;AAAA,UAC3B;AAAA,QACF;AAEA,YAAI,KAAK,WAAW;AAClB,eAAK,WAAW;AAAA,QAClB,OAAO;AACL,eAAK,MAAM,OAAO,IAAI;AAAA,QACxB;AAAA,MACF;AAEA,WAAK,MAAM,OAAO;AAAA,QAChB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,WAAO,KAAK,UAAU;AAAA,EACxB;AAEA,SAAO,aAAa,SAAS,aAAa;AACxC,QAAI,CAAC,KAAK,MAAM,eAAe;AAC7B,WAAK,SAAS;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,QAAQ,SAAS,MAAM,SAAS,cAAc;AACnD,QAAI,SAAS,MACT,uBACA,uBACA;AAEJ,QAAI,KAAK,MAAM,YAAY;AACzB,UAAI,KAAK,MAAM,kBAAkB,gBAAgB,OAAO,SAAS,aAAa,gBAAgB;AAE5F,aAAK,OAAO;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,WAAW,KAAK,SAAS;AACvB,YAAI;AAGJ,SAAC,iBAAiB,KAAK,YAAY,OAAO,SAAS,eAAe,cAAc;AAEhF,eAAO,KAAK;AAAA,MACd;AAAA,IACF;AAGA,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;AAAA,IACzB;AAIA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,UAAI,WAAW,KAAK,UAAU,KAAK,SAAU,GAAG;AAC9C,eAAO,EAAE,QAAQ;AAAA,MACnB,CAAC;AAED,UAAI,UAAU;AACZ,aAAK,WAAW,SAAS,OAAO;AAAA,MAClC;AAAA,IACF;AAEA,QAAI,WAAW,oBAAoB,KAAK,QAAQ;AAChD,QAAI,kBAAkB,mBAAmB;AAEzC,QAAI,iBAAiB;AAAA,MACnB;AAAA,MACA,WAAW;AAAA,MACX,MAAM,KAAK;AAAA,IACb;AACA,WAAO,eAAe,gBAAgB,UAAU;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,YAAI,iBAAiB;AACnB,iBAAO,sBAAsB;AAC7B,iBAAO,gBAAgB;AAAA,QACzB;AAEA,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,QAAI,UAAU,SAASC,WAAU;AAC/B,UAAI,CAAC,OAAO,QAAQ,SAAS;AAC3B,eAAO,QAAQ,OAAO,iBAAiB;AAAA,MACzC;AAEA,aAAO,sBAAsB;AAC7B,aAAO,OAAO,QAAQ,QAAQ,cAAc;AAAA,IAC9C;AAGA,QAAI,UAAU;AAAA,MACZ;AAAA,MACA,SAAS,KAAK;AAAA,MACd;AAAA,MACA,OAAO,KAAK;AAAA,MACZ;AAAA,MACA,MAAM,KAAK;AAAA,IACb;AAEA,SAAK,wBAAwB,KAAK,QAAQ,aAAa,OAAO,SAAS,sBAAsB,SAAS;AACpG,UAAI;AAEJ,OAAC,yBAAyB,KAAK,QAAQ,aAAa,OAAO,SAAS,uBAAuB,QAAQ,OAAO;AAAA,IAC5G;AAGA,SAAK,cAAc,KAAK;AAExB,QAAI,CAAC,KAAK,MAAM,cAAc,KAAK,MAAM,gBAAgB,wBAAwB,QAAQ,iBAAiB,OAAO,SAAS,sBAAsB,OAAO;AACrJ,UAAI;AAEJ,WAAK,SAAS;AAAA,QACZ,MAAM;AAAA,QACN,OAAO,yBAAyB,QAAQ,iBAAiB,OAAO,SAAS,uBAAuB;AAAA,MAClG,CAAC;AAAA,IACH;AAGA,SAAK,UAAU,IAAI,QAAQ;AAAA,MACzB,IAAI,QAAQ;AAAA,MACZ,OAAO,mBAAmB,OAAO,UAAU,wBAAwB,gBAAgB,UAAU,OAAO,SAAS,sBAAsB,KAAK,eAAe;AAAA,MACvJ,WAAW,SAAS,UAAU,MAAM;AAClC,eAAO,QAAQ,IAAI;AAGnB,eAAO,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO,MAAM,OAAO,UAAU,MAAM,MAAM;AAE3F,YAAI,OAAO,cAAc,GAAG;AAC1B,iBAAO,eAAe;AAAA,QACxB;AAAA,MACF;AAAA,MACA,SAAS,SAAS,QAAQ,OAAO;AAE/B,YAAI,EAAE,iBAAiB,KAAK,KAAK,MAAM,SAAS;AAC9C,iBAAO,SAAS;AAAA,YACd,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAEA,YAAI,CAAC,iBAAiB,KAAK,GAAG;AAE5B,iBAAO,MAAM,OAAO,WAAW,OAAO,SAAS,OAAO,MAAM,OAAO,QAAQ,OAAO,MAAM;AAExF,oBAAU,EAAE,MAAM,KAAK;AAAA,QACzB;AAGA,YAAI,OAAO,cAAc,GAAG;AAC1B,iBAAO,eAAe;AAAA,QACxB;AAAA,MACF;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,SAAS,SAAS,UAAU;AAC1B,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,YAAY,SAAS,aAAa;AAChC,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,OAAO,QAAQ,QAAQ;AAAA,MACvB,YAAY,QAAQ,QAAQ;AAAA,IAC9B,CAAC;AACD,SAAK,UAAU,KAAK,QAAQ;AAC5B,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,WAAW,SAAS,SAAS,QAAQ;AAC1C,QAAI,SAAS;AAEb,SAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO,MAAM;AAC5C,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,iBAAS,cAAc,MAAM;AAAA,MAC/B,CAAC;AAED,aAAO,MAAM,OAAO;AAAA,QAClB,OAAO;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,kBAAkB,SAASC,iBAAgB,SAAS;AACzD,QAAI,OAAO,OAAO,QAAQ,gBAAgB,aAAa,QAAQ,YAAY,IAAI,QAAQ;AACvF,QAAI,iBAAiB,OAAO,QAAQ,gBAAgB;AACpD,QAAI,uBAAuB,iBAAiB,OAAO,QAAQ,yBAAyB,aAAa,QAAQ,qBAAqB,IAAI,QAAQ,uBAAuB;AACjK,QAAI,UAAU,OAAO,SAAS;AAC9B,WAAO;AAAA,MACL;AAAA,MACA,iBAAiB;AAAA,MACjB,eAAe,UAAU,wBAAwB,OAAO,uBAAuB,KAAK,IAAI,IAAI;AAAA,MAC5F,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,MACnB,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,eAAe;AAAA,MACf,UAAU;AAAA,MACV,QAAQ,UAAU,YAAY;AAAA,IAChC;AAAA,EACF;AAEA,SAAO,UAAU,SAASC,SAAQ,OAAO,QAAQ;AAC/C,QAAI,cAAc;AAElB,YAAQ,OAAO,MAAM;AAAA,MACnB,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,mBAAmB,MAAM,oBAAoB;AAAA,QAC/C,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,UAAU;AAAA,QACZ,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,UAAU;AAAA,QACZ,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,mBAAmB;AAAA,UACnB,YAAY,eAAe,OAAO,SAAS,OAAO,eAAe;AAAA,UACjE,YAAY;AAAA,UACZ,UAAU;AAAA,QACZ,GAAG,CAAC,MAAM,iBAAiB;AAAA,UACzB,OAAO;AAAA,UACP,QAAQ;AAAA,QACV,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,MAAM,OAAO;AAAA,UACb,iBAAiB,MAAM,kBAAkB;AAAA,UACzC,gBAAgB,wBAAwB,OAAO,kBAAkB,OAAO,wBAAwB,KAAK,IAAI;AAAA,UACzG,OAAO;AAAA,UACP,mBAAmB;AAAA,UACnB,YAAY;AAAA,UACZ,eAAe;AAAA,UACf,UAAU;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAAA,MAEH,KAAK;AACH,YAAI,QAAQ,OAAO;AAEnB,YAAI,iBAAiB,KAAK,KAAK,MAAM,UAAU,KAAK,aAAa;AAC/D,iBAAO,SAAS,CAAC,GAAG,KAAK,WAAW;AAAA,QACtC;AAEA,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB;AAAA,UACA,kBAAkB,MAAM,mBAAmB;AAAA,UAC3C,gBAAgB,KAAK,IAAI;AAAA,UACzB,mBAAmB,MAAM,oBAAoB;AAAA,UAC7C,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO;AAAA,UACzB,eAAe;AAAA,QACjB,CAAC;AAAA,MAEH,KAAK;AACH,eAAO,SAAS,CAAC,GAAG,OAAO,OAAO,KAAK;AAAA,MAEzC;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAEA,SAAOJ;AACT,EAAE;;;AG7eK,IAAI,aAA0B,SAAU,eAAe;AAC5D,iBAAeK,aAAY,aAAa;AAExC,WAASA,YAAW,QAAQ;AAC1B,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AACpC,UAAM,SAAS,UAAU,CAAC;AAC1B,UAAM,UAAU,CAAC;AACjB,UAAM,aAAa,CAAC;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,YAAW;AAExB,SAAO,QAAQ,SAAS,MAAM,QAAQ,SAAS,OAAO;AACpD,QAAI;AAEJ,QAAI,WAAW,QAAQ;AACvB,QAAI,aAAa,qBAAqB,QAAQ,cAAc,OAAO,qBAAqB,sBAAsB,UAAU,OAAO;AAC/H,QAAI,QAAQ,KAAK,IAAI,SAAS;AAE9B,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,MAAM;AAAA,QAChB,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,SAAS,OAAO,oBAAoB,OAAO;AAAA,QAC3C;AAAA,QACA,gBAAgB,OAAO,iBAAiB,QAAQ;AAAA,QAChD,MAAM,QAAQ;AAAA,MAChB,CAAC;AACD,WAAK,IAAI,KAAK;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,SAAS,IAAI,OAAO;AAC/B,QAAI,CAAC,KAAK,WAAW,MAAM,SAAS,GAAG;AACrC,WAAK,WAAW,MAAM,SAAS,IAAI;AACnC,WAAK,QAAQ,KAAK,KAAK;AACvB,WAAK,OAAO;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,OAAO,OAAO;AACrC,QAAI,aAAa,KAAK,WAAW,MAAM,SAAS;AAEhD,QAAI,YAAY;AACd,YAAM,QAAQ;AACd,WAAK,UAAU,KAAK,QAAQ,OAAO,SAAU,GAAG;AAC9C,eAAO,MAAM;AAAA,MACf,CAAC;AAED,UAAI,eAAe,OAAO;AACxB,eAAO,KAAK,WAAW,MAAM,SAAS;AAAA,MACxC;AAEA,WAAK,OAAO;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,QAAQ,QAAQ,SAAU,OAAO;AACtC,eAAO,OAAO,KAAK;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,SAAS,IAAI,WAAW;AACnC,WAAO,KAAK,WAAW,SAAS;AAAA,EAClC;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,OAAO,SAAS,KAAK,MAAM,MAAM;AACtC,QAAI,mBAAmB,gBAAgB,MAAM,IAAI,GAC7C,UAAU,iBAAiB,CAAC;AAEhC,QAAI,OAAO,QAAQ,UAAU,aAAa;AACxC,cAAQ,QAAQ;AAAA,IAClB;AAEA,WAAO,KAAK,QAAQ,KAAK,SAAU,OAAO;AACxC,aAAO,WAAW,SAAS,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AAEA,SAAO,UAAU,SAAS,QAAQ,MAAM,MAAM;AAC5C,QAAI,oBAAoB,gBAAgB,MAAM,IAAI,GAC9C,UAAU,kBAAkB,CAAC;AAEjC,WAAO,OAAO,KAAK,OAAO,EAAE,SAAS,IAAI,KAAK,QAAQ,OAAO,SAAU,OAAO;AAC5E,aAAO,WAAW,SAAS,KAAK;AAAA,IAClC,CAAC,IAAI,KAAK;AAAA,EACZ;AAEA,SAAO,SAAS,SAAS,OAAO,OAAO;AACrC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,QAAQ,QAAQ,SAAU,OAAO;AACtC,cAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,QAAQ,QAAQ,SAAU,OAAO;AACtC,cAAM,SAAS;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAOA;AACT,EAAE,YAAY;;;AClJd;;;ACAA;AAOO,IAAI,WAAwB,WAAY;AAC7C,WAASC,UAAS,QAAQ;AACxB,SAAK,UAAU,SAAS,CAAC,GAAG,OAAO,gBAAgB,OAAO,OAAO;AACjE,SAAK,aAAa,OAAO;AACzB,SAAK,gBAAgB,OAAO;AAC5B,SAAK,YAAY,CAAC;AAClB,SAAK,QAAQ,OAAO,SAAS,gBAAgB;AAC7C,SAAK,OAAO,OAAO;AAAA,EACrB;AAEA,MAAI,SAASA,UAAS;AAEtB,SAAO,WAAW,SAAS,SAAS,OAAO;AACzC,SAAK,SAAS;AAAA,MACZ,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,cAAc,SAAS,YAAY,UAAU;AAClD,QAAI,KAAK,UAAU,QAAQ,QAAQ,MAAM,IAAI;AAC3C,WAAK,UAAU,KAAK,QAAQ;AAAA,IAC9B;AAAA,EACF;AAEA,SAAO,iBAAiB,SAAS,eAAe,UAAU;AACxD,SAAK,YAAY,KAAK,UAAU,OAAO,SAAU,GAAG;AAClD,aAAO,MAAM;AAAA,IACf,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,OAAO;AACpB,aAAO,KAAK,QAAQ,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,IACnD;AAEA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,SAAO,WAAW,SAAS,YAAY;AACrC,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,SAAS;AACtB,aAAO,KAAK,QAAQ;AAAA,IACtB;AAEA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI,QAAQ;AAEZ,QAAI;AACJ,QAAI,WAAW,KAAK,MAAM,WAAW;AACrC,QAAI,UAAU,QAAQ,QAAQ;AAE9B,QAAI,CAAC,UAAU;AACb,WAAK,SAAS;AAAA,QACZ,MAAM;AAAA,QACN,WAAW,KAAK,QAAQ;AAAA,MAC1B,CAAC;AACD,gBAAU,QAAQ,KAAK,WAAY;AAEjC,cAAM,cAAc,OAAO,YAAY,OAAO,SAAS,MAAM,cAAc,OAAO,SAAS,MAAM,MAAM,WAAW,KAAK;AAAA,MACzH,CAAC,EAAE,KAAK,WAAY;AAClB,eAAO,MAAM,QAAQ,YAAY,OAAO,SAAS,MAAM,QAAQ,SAAS,MAAM,MAAM,SAAS;AAAA,MAC/F,CAAC,EAAE,KAAK,SAAU,SAAS;AACzB,YAAI,YAAY,MAAM,MAAM,SAAS;AACnC,gBAAM,SAAS;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA,WAAW,MAAM,MAAM;AAAA,UACzB,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,QAAQ,KAAK,WAAY;AAC9B,aAAO,MAAM,gBAAgB;AAAA,IAC/B,CAAC,EAAE,KAAK,SAAU,QAAQ;AACxB,aAAO;AAEP,YAAM,cAAc,OAAO,aAAa,OAAO,SAAS,MAAM,cAAc,OAAO,UAAU,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,SAAS,KAAK;AAAA,IACtJ,CAAC,EAAE,KAAK,WAAY;AAClB,aAAO,MAAM,QAAQ,aAAa,OAAO,SAAS,MAAM,QAAQ,UAAU,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO;AAAA,IAC5H,CAAC,EAAE,KAAK,WAAY;AAClB,aAAO,MAAM,QAAQ,aAAa,OAAO,SAAS,MAAM,QAAQ,UAAU,MAAM,MAAM,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO;AAAA,IAClI,CAAC,EAAE,KAAK,WAAY;AAClB,YAAM,SAAS;AAAA,QACb,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAED,aAAO;AAAA,IACT,CAAC,EAAE,MAAM,SAAU,OAAO;AAExB,YAAM,cAAc,OAAO,WAAW,OAAO,SAAS,MAAM,cAAc,OAAO,QAAQ,OAAO,MAAM,MAAM,WAAW,MAAM,MAAM,SAAS,KAAK;AAEjJ,gBAAU,EAAE,MAAM,KAAK;AACvB,aAAO,QAAQ,QAAQ,EAAE,KAAK,WAAY;AACxC,eAAO,MAAM,QAAQ,WAAW,OAAO,SAAS,MAAM,QAAQ,QAAQ,OAAO,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO;AAAA,MACzH,CAAC,EAAE,KAAK,WAAY;AAClB,eAAO,MAAM,QAAQ,aAAa,OAAO,SAAS,MAAM,QAAQ,UAAU,QAAW,OAAO,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO;AAAA,MACxI,CAAC,EAAE,KAAK,WAAY;AAClB,cAAM,SAAS;AAAA,UACb,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAED,cAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,kBAAkB,SAAS,kBAAkB;AAClD,QAAI,SAAS,MACT;AAEJ,SAAK,UAAU,IAAI,QAAQ;AAAA,MACzB,IAAI,SAAS,KAAK;AAChB,YAAI,CAAC,OAAO,QAAQ,YAAY;AAC9B,iBAAO,QAAQ,OAAO,qBAAqB;AAAA,QAC7C;AAEA,eAAO,OAAO,QAAQ,WAAW,OAAO,MAAM,SAAS;AAAA,MACzD;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,SAAS,SAAS,UAAU;AAC1B,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,YAAY,SAAS,aAAa;AAChC,eAAO,SAAS;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,sBAAsB,KAAK,QAAQ,UAAU,OAAO,sBAAsB;AAAA,MAClF,YAAY,KAAK,QAAQ;AAAA,IAC3B,CAAC;AACD,WAAO,KAAK,QAAQ;AAAA,EACtB;AAEA,SAAO,WAAW,SAAS,SAAS,QAAQ;AAC1C,QAAI,SAAS;AAEb,SAAK,QAAQ,QAAQ,KAAK,OAAO,MAAM;AACvC,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,iBAAS,iBAAiB,MAAM;AAAA,MAClC,CAAC;AAED,aAAO,cAAc,OAAO,MAAM;AAAA,IACpC,CAAC;AAAA,EACH;AAEA,SAAOA;AACT,EAAE;AACK,SAAS,kBAAkB;AAChC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,EACb;AACF;AAEA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,cAAc,MAAM,eAAe;AAAA,MACrC,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,UAAU;AAAA,MACZ,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,UAAU;AAAA,MACZ,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,SAAS,OAAO;AAAA,QAChB,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,WAAW,OAAO;AAAA,MACpB,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,MAAM,OAAO;AAAA,QACb,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,MACZ,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,OAAO,OAAO;AAAA,QACd,cAAc,MAAM,eAAe;AAAA,QACnC,UAAU;AAAA,QACV,QAAQ;AAAA,MACV,CAAC;AAAA,IAEH,KAAK;AACH,aAAO,SAAS,CAAC,GAAG,OAAO,OAAO,KAAK;AAAA,IAEzC;AACE,aAAO;AAAA,EACX;AACF;;;AChOO,IAAI,gBAA6B,SAAU,eAAe;AAC/D,iBAAeC,gBAAe,aAAa;AAE3C,WAASA,eAAc,QAAQ;AAC7B,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AACpC,UAAM,SAAS,UAAU,CAAC;AAC1B,UAAM,YAAY,CAAC;AACnB,UAAM,aAAa;AACnB,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,eAAc;AAE3B,SAAO,QAAQ,SAAS,MAAM,QAAQ,SAAS,OAAO;AACpD,QAAI,WAAW,IAAI,SAAS;AAAA,MAC1B,eAAe;AAAA,MACf,YAAY,EAAE,KAAK;AAAA,MACnB,SAAS,OAAO,uBAAuB,OAAO;AAAA,MAC9C;AAAA,MACA,gBAAgB,QAAQ,cAAc,OAAO,oBAAoB,QAAQ,WAAW,IAAI;AAAA,MACxF,MAAM,QAAQ;AAAA,IAChB,CAAC;AACD,SAAK,IAAI,QAAQ;AACjB,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,SAAS,IAAI,UAAU;AAClC,SAAK,UAAU,KAAK,QAAQ;AAC5B,SAAK,OAAO,QAAQ;AAAA,EACtB;AAEA,SAAO,SAAS,SAAS,OAAO,UAAU;AACxC,SAAK,YAAY,KAAK,UAAU,OAAO,SAAU,GAAG;AAClD,aAAO,MAAM;AAAA,IACf,CAAC;AACD,aAAS,OAAO;AAChB,SAAK,OAAO,QAAQ;AAAA,EACtB;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,eAAO,OAAO,QAAQ;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,OAAO,SAAS,KAAK,SAAS;AACnC,QAAI,OAAO,QAAQ,UAAU,aAAa;AACxC,cAAQ,QAAQ;AAAA,IAClB;AAEA,WAAO,KAAK,UAAU,KAAK,SAAU,UAAU;AAC7C,aAAO,cAAc,SAAS,QAAQ;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,SAAO,UAAU,SAAS,QAAQ,SAAS;AACzC,WAAO,KAAK,UAAU,OAAO,SAAU,UAAU;AAC/C,aAAO,cAAc,SAAS,QAAQ;AAAA,IACxC,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,SAAS,OAAO,UAAU;AACxC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,SAAK,sBAAsB;AAAA,EAC7B;AAEA,SAAO,WAAW,SAAS,WAAW;AACpC,SAAK,sBAAsB;AAAA,EAC7B;AAEA,SAAO,wBAAwB,SAAS,wBAAwB;AAC9D,QAAI,kBAAkB,KAAK,UAAU,OAAO,SAAU,GAAG;AACvD,aAAO,EAAE,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,cAAc,MAAM,WAAY;AACrC,aAAO,gBAAgB,OAAO,SAAU,SAAS,UAAU;AACzD,eAAO,QAAQ,KAAK,WAAY;AAC9B,iBAAO,SAAS,SAAS,EAAE,MAAM,IAAI;AAAA,QACvC,CAAC;AAAA,MACH,GAAG,QAAQ,QAAQ,CAAC;AAAA,IACtB,CAAC;AAAA,EACH;AAEA,SAAOA;AACT,EAAE,YAAY;;;AC5GP,SAAS,wBAAwB;AACtC,SAAO;AAAA,IACL,SAAS,SAAS,QAAQ,SAAS;AACjC,cAAQ,UAAU,WAAY;AAC5B,YAAI,uBAAuB,wBAAwB,wBAAwB,wBAAwB,qBAAqB;AAExH,YAAI,eAAe,wBAAwB,QAAQ,iBAAiB,OAAO,UAAU,yBAAyB,sBAAsB,SAAS,OAAO,SAAS,uBAAuB;AACpL,YAAI,aAAa,yBAAyB,QAAQ,iBAAiB,OAAO,UAAU,yBAAyB,uBAAuB,SAAS,OAAO,SAAS,uBAAuB;AACpL,YAAI,YAAY,aAAa,OAAO,SAAS,UAAU;AACvD,YAAI,sBAAsB,aAAa,OAAO,SAAS,UAAU,eAAe;AAChF,YAAI,0BAA0B,aAAa,OAAO,SAAS,UAAU,eAAe;AACpF,YAAI,aAAa,sBAAsB,QAAQ,MAAM,SAAS,OAAO,SAAS,oBAAoB,UAAU,CAAC;AAC7G,YAAI,kBAAkB,uBAAuB,QAAQ,MAAM,SAAS,OAAO,SAAS,qBAAqB,eAAe,CAAC;AACzH,YAAI,kBAAkB,mBAAmB;AACzC,YAAI,cAAc,mBAAmB,OAAO,SAAS,gBAAgB;AACrE,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAEhB,YAAI,UAAU,QAAQ,QAAQ,WAAW,WAAY;AACnD,iBAAO,QAAQ,OAAO,iBAAiB;AAAA,QACzC;AAEA,YAAI,gBAAgB,SAASC,eAAc,OAAOC,QAAO,MAAM,UAAU;AACvE,0BAAgB,WAAW,CAACA,MAAK,EAAE,OAAO,aAAa,IAAI,CAAC,EAAE,OAAO,eAAe,CAACA,MAAK,CAAC;AAC3F,iBAAO,WAAW,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC;AAAA,QAClE;AAGA,YAAI,YAAY,SAASC,WAAU,OAAOC,SAAQF,QAAO,UAAU;AACjE,cAAI,WAAW;AACb,mBAAO,QAAQ,OAAO,WAAW;AAAA,UACnC;AAEA,cAAI,OAAOA,WAAU,eAAe,CAACE,WAAU,MAAM,QAAQ;AAC3D,mBAAO,QAAQ,QAAQ,KAAK;AAAA,UAC9B;AAEA,cAAI,iBAAiB;AAAA,YACnB,UAAU,QAAQ;AAAA,YAClB,QAAQ;AAAA,YACR,WAAWF;AAAA,YACX,MAAM,QAAQ;AAAA,UAChB;AACA,cAAI,gBAAgB,QAAQ,cAAc;AAC1C,cAAIG,WAAU,QAAQ,QAAQ,aAAa,EAAE,KAAK,SAAU,MAAM;AAChE,mBAAO,cAAc,OAAOH,QAAO,MAAM,QAAQ;AAAA,UACnD,CAAC;AAED,cAAI,aAAa,aAAa,GAAG;AAC/B,gBAAI,eAAeG;AACnB,yBAAa,SAAS,cAAc;AAAA,UACtC;AAEA,iBAAOA;AAAA,QACT;AAEA,YAAI;AAEJ,YAAI,CAAC,SAAS,QAAQ;AACpB,oBAAU,UAAU,CAAC,CAAC;AAAA,QACxB,WACS,oBAAoB;AACzB,cAAI,SAAS,OAAO,cAAc;AAClC,cAAI,QAAQ,SAAS,YAAY,iBAAiB,QAAQ,SAAS,QAAQ;AAC3E,oBAAU,UAAU,UAAU,QAAQ,KAAK;AAAA,QAC7C,WACS,wBAAwB;AAC7B,cAAI,UAAU,OAAO,cAAc;AAEnC,cAAI,SAAS,UAAU,YAAY,qBAAqB,QAAQ,SAAS,QAAQ;AAEjF,oBAAU,UAAU,UAAU,SAAS,QAAQ,IAAI;AAAA,QACrD,OACK;AACD,WAAC,WAAY;AACX,4BAAgB,CAAC;AACjB,gBAAID,UAAS,OAAO,QAAQ,QAAQ,qBAAqB;AACzD,gBAAI,uBAAuB,eAAe,SAAS,CAAC,IAAI,YAAY,SAAS,CAAC,GAAG,GAAG,QAAQ,IAAI;AAEhG,sBAAU,uBAAuB,UAAU,CAAC,GAAGA,SAAQ,cAAc,CAAC,CAAC,IAAI,QAAQ,QAAQ,cAAc,CAAC,GAAG,cAAc,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AAE3I,gBAAI,QAAQ,SAASE,OAAMC,IAAG;AAC5B,wBAAU,QAAQ,KAAK,SAAU,OAAO;AACtC,oBAAI,sBAAsB,eAAe,SAASA,EAAC,IAAI,YAAY,SAASA,EAAC,GAAGA,IAAG,QAAQ,IAAI;AAE/F,oBAAI,qBAAqB;AACvB,sBAAI,UAAUH,UAAS,cAAcG,EAAC,IAAI,iBAAiB,QAAQ,SAAS,KAAK;AAEjF,yBAAO,UAAU,OAAOH,SAAQ,OAAO;AAAA,gBACzC;AAEA,uBAAO,QAAQ,QAAQ,cAAc,OAAO,cAAcG,EAAC,GAAG,SAASA,EAAC,CAAC,CAAC;AAAA,cAC5E,CAAC;AAAA,YACH;AAEA,qBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,oBAAM,CAAC;AAAA,YACT;AAAA,UACF,GAAG;AAAA,QACL;AAEN,YAAI,eAAe,QAAQ,KAAK,SAAU,OAAO;AAC/C,iBAAO;AAAA,YACL;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF,CAAC;AACD,YAAI,oBAAoB;AAExB,0BAAkB,SAAS,WAAY;AACrC,sBAAY;AACZ,6BAAmB,OAAO,SAAS,gBAAgB,MAAM;AAEzD,cAAI,aAAa,OAAO,GAAG;AACzB,oBAAQ,OAAO;AAAA,UACjB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AACO,SAAS,iBAAiB,SAAS,OAAO;AAC/C,SAAO,QAAQ,oBAAoB,OAAO,SAAS,QAAQ,iBAAiB,MAAM,MAAM,SAAS,CAAC,GAAG,KAAK;AAC5G;AACO,SAAS,qBAAqB,SAAS,OAAO;AACnD,SAAO,QAAQ,wBAAwB,OAAO,SAAS,QAAQ,qBAAqB,MAAM,CAAC,GAAG,KAAK;AACrG;AAMO,SAAS,YAAY,SAAS,OAAO;AAC1C,MAAI,QAAQ,oBAAoB,MAAM,QAAQ,KAAK,GAAG;AACpD,QAAI,gBAAgB,iBAAiB,SAAS,KAAK;AACnD,WAAO,OAAO,kBAAkB,eAAe,kBAAkB,QAAQ,kBAAkB;AAAA,EAC7F;AACF;AAMO,SAAS,gBAAgB,SAAS,OAAO;AAC9C,MAAI,QAAQ,wBAAwB,MAAM,QAAQ,KAAK,GAAG;AACxD,QAAI,oBAAoB,qBAAqB,SAAS,KAAK;AAC3D,WAAO,OAAO,sBAAsB,eAAe,sBAAsB,QAAQ,sBAAsB;AAAA,EACzG;AACF;;;AH/IO,IAAI,cAA2B,WAAY;AAChD,WAASC,aAAY,QAAQ;AAC3B,QAAI,WAAW,QAAQ;AACrB,eAAS,CAAC;AAAA,IACZ;AAEA,SAAK,aAAa,OAAO,cAAc,IAAI,WAAW;AACtD,SAAK,gBAAgB,OAAO,iBAAiB,IAAI,cAAc;AAC/D,SAAK,iBAAiB,OAAO,kBAAkB,CAAC;AAChD,SAAK,gBAAgB,CAAC;AACtB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAEA,MAAI,SAASA,aAAY;AAEzB,SAAO,QAAQ,SAAS,QAAQ;AAC9B,QAAI,QAAQ;AAEZ,SAAK,mBAAmB,aAAa,UAAU,WAAY;AACzD,UAAI,aAAa,UAAU,KAAK,cAAc,SAAS,GAAG;AACxD,cAAM,cAAc,QAAQ;AAE5B,cAAM,WAAW,QAAQ;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,SAAK,oBAAoB,cAAc,UAAU,WAAY;AAC3D,UAAI,aAAa,UAAU,KAAK,cAAc,SAAS,GAAG;AACxD,cAAM,cAAc,SAAS;AAE7B,cAAM,WAAW,SAAS;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI,uBAAuB;AAE3B,KAAC,wBAAwB,KAAK,qBAAqB,OAAO,SAAS,sBAAsB,KAAK,IAAI;AAClG,KAAC,wBAAwB,KAAK,sBAAsB,OAAO,SAAS,sBAAsB,KAAK,IAAI;AAAA,EACrG;AAEA,SAAO,aAAa,SAAS,WAAW,MAAM,MAAM;AAClD,QAAI,mBAAmB,gBAAgB,MAAM,IAAI,GAC7C,UAAU,iBAAiB,CAAC;AAEhC,YAAQ,WAAW;AACnB,WAAO,KAAK,WAAW,QAAQ,OAAO,EAAE;AAAA,EAC1C;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS;AAC/C,WAAO,KAAK,cAAc,QAAQ,SAAS,CAAC,GAAG,SAAS;AAAA,MACtD,UAAU;AAAA,IACZ,CAAC,CAAC,EAAE;AAAA,EACN;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU,SAAS;AAC7D,QAAI;AAEJ,YAAQ,wBAAwB,KAAK,WAAW,KAAK,UAAU,OAAO,MAAM,OAAO,SAAS,sBAAsB,MAAM;AAAA,EAC1H;AAEA,SAAO,iBAAiB,SAAS,eAAe,mBAAmB;AACjE,WAAO,KAAK,cAAc,EAAE,QAAQ,iBAAiB,EAAE,IAAI,SAAU,MAAM;AACzE,UAAI,WAAW,KAAK,UAChB,QAAQ,KAAK;AACjB,UAAI,OAAO,MAAM;AACjB,aAAO,CAAC,UAAU,IAAI;AAAA,IACxB,CAAC;AAAA,EACH;AAEA,SAAO,eAAe,SAAS,aAAa,UAAU,SAAS,SAAS;AACtE,QAAI,gBAAgB,eAAe,QAAQ;AAC3C,QAAI,mBAAmB,KAAK,oBAAoB,aAAa;AAC7D,WAAO,KAAK,WAAW,MAAM,MAAM,gBAAgB,EAAE,QAAQ,SAAS,OAAO;AAAA,EAC/E;AAEA,SAAO,iBAAiB,SAAS,eAAe,mBAAmB,SAAS,SAAS;AACnF,QAAI,SAAS;AAEb,WAAO,cAAc,MAAM,WAAY;AACrC,aAAO,OAAO,cAAc,EAAE,QAAQ,iBAAiB,EAAE,IAAI,SAAU,OAAO;AAC5E,YAAI,WAAW,MAAM;AACrB,eAAO,CAAC,UAAU,OAAO,aAAa,UAAU,SAAS,OAAO,CAAC;AAAA,MACnE,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,gBAAgB,SAAS,cAAc,UAAU,SAAS;AAC/D,QAAI;AAEJ,YAAQ,yBAAyB,KAAK,WAAW,KAAK,UAAU,OAAO,MAAM,OAAO,SAAS,uBAAuB;AAAA,EACtH;AAEA,SAAO,gBAAgB,SAAS,cAAc,MAAM,MAAM;AACxD,QAAI,oBAAoB,gBAAgB,MAAM,IAAI,GAC9C,UAAU,kBAAkB,CAAC;AAEjC,QAAI,aAAa,KAAK;AACtB,kBAAc,MAAM,WAAY;AAC9B,iBAAW,QAAQ,OAAO,EAAE,QAAQ,SAAU,OAAO;AACnD,mBAAW,OAAO,KAAK;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,eAAe,SAAS,aAAa,MAAM,MAAM,MAAM;AAC5D,QAAI,SAAS;AAEb,QAAI,oBAAoB,gBAAgB,MAAM,MAAM,IAAI,GACpD,UAAU,kBAAkB,CAAC,GAC7B,UAAU,kBAAkB,CAAC;AAEjC,QAAI,aAAa,KAAK;AAEtB,QAAI,iBAAiB,SAAS,CAAC,GAAG,SAAS;AAAA,MACzC,QAAQ;AAAA,IACV,CAAC;AAED,WAAO,cAAc,MAAM,WAAY;AACrC,iBAAW,QAAQ,OAAO,EAAE,QAAQ,SAAU,OAAO;AACnD,cAAM,MAAM;AAAA,MACd,CAAC;AACD,aAAO,OAAO,eAAe,gBAAgB,OAAO;AAAA,IACtD,CAAC;AAAA,EACH;AAEA,SAAO,gBAAgB,SAAS,cAAc,MAAM,MAAM,MAAM;AAC9D,QAAI,SAAS;AAEb,QAAI,oBAAoB,gBAAgB,MAAM,MAAM,IAAI,GACpD,UAAU,kBAAkB,CAAC,GAC7B,qBAAqB,kBAAkB,CAAC,GACxC,gBAAgB,uBAAuB,SAAS,CAAC,IAAI;AAEzD,QAAI,OAAO,cAAc,WAAW,aAAa;AAC/C,oBAAc,SAAS;AAAA,IACzB;AAEA,QAAI,WAAW,cAAc,MAAM,WAAY;AAC7C,aAAO,OAAO,WAAW,QAAQ,OAAO,EAAE,IAAI,SAAU,OAAO;AAC7D,eAAO,MAAM,OAAO,aAAa;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AACD,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACpD;AAEA,SAAO,oBAAoB,SAAS,kBAAkB,MAAM,MAAM,MAAM;AACtE,QAAI,OACA,uBACA,uBACA,SAAS;AAEb,QAAI,oBAAoB,gBAAgB,MAAM,MAAM,IAAI,GACpD,UAAU,kBAAkB,CAAC,GAC7B,UAAU,kBAAkB,CAAC;AAEjC,QAAI,iBAAiB,SAAS,CAAC,GAAG,SAAS;AAAA;AAAA;AAAA,MAGzC,SAAS,SAAS,wBAAwB,QAAQ,kBAAkB,OAAO,wBAAwB,QAAQ,WAAW,OAAO,QAAQ;AAAA,MACrI,WAAW,wBAAwB,QAAQ,oBAAoB,OAAO,wBAAwB;AAAA,IAChG,CAAC;AAED,WAAO,cAAc,MAAM,WAAY;AACrC,aAAO,WAAW,QAAQ,OAAO,EAAE,QAAQ,SAAU,OAAO;AAC1D,cAAM,WAAW;AAAA,MACnB,CAAC;AAED,aAAO,OAAO,eAAe,gBAAgB,OAAO;AAAA,IACtD,CAAC;AAAA,EACH;AAEA,SAAO,iBAAiB,SAAS,eAAe,MAAM,MAAM,MAAM;AAChE,QAAI,SAAS;AAEb,QAAI,oBAAoB,gBAAgB,MAAM,MAAM,IAAI,GACpD,UAAU,kBAAkB,CAAC,GAC7B,UAAU,kBAAkB,CAAC;AAEjC,QAAI,WAAW,cAAc,MAAM,WAAY;AAC7C,aAAO,OAAO,WAAW,QAAQ,OAAO,EAAE,IAAI,SAAU,OAAO;AAC7D,eAAO,MAAM,MAAM,QAAW,SAAS,CAAC,GAAG,SAAS;AAAA,UAClD,MAAM;AAAA,YACJ,aAAa,WAAW,OAAO,SAAS,QAAQ;AAAA,UAClD;AAAA,QACF,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,IACH,CAAC;AACD,QAAI,UAAU,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI;AAE7C,QAAI,EAAE,WAAW,OAAO,SAAS,QAAQ,eAAe;AACtD,gBAAU,QAAQ,MAAM,IAAI;AAAA,IAC9B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,aAAa,SAAS,WAAW,MAAM,MAAM,MAAM;AACxD,QAAI,gBAAgB,eAAe,MAAM,MAAM,IAAI;AACnD,QAAI,mBAAmB,KAAK,oBAAoB,aAAa;AAE7D,QAAI,OAAO,iBAAiB,UAAU,aAAa;AACjD,uBAAiB,QAAQ;AAAA,IAC3B;AAEA,QAAI,QAAQ,KAAK,WAAW,MAAM,MAAM,gBAAgB;AACxD,WAAO,MAAM,cAAc,iBAAiB,SAAS,IAAI,MAAM,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,MAAM,MAAM,IAAI;AAAA,EAC3H;AAEA,SAAO,gBAAgB,SAAS,cAAc,MAAM,MAAM,MAAM;AAC9D,WAAO,KAAK,WAAW,MAAM,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EAChE;AAEA,SAAO,qBAAqB,SAAS,mBAAmB,MAAM,MAAM,MAAM;AACxE,QAAI,gBAAgB,eAAe,MAAM,MAAM,IAAI;AACnD,kBAAc,WAAW,sBAAsB;AAC/C,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAEA,SAAO,wBAAwB,SAAS,sBAAsB,MAAM,MAAM,MAAM;AAC9E,WAAO,KAAK,mBAAmB,MAAM,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACxE;AAEA,SAAO,kBAAkB,SAAS,kBAAkB;AAClD,QAAI,SAAS;AAEb,QAAI,WAAW,cAAc,MAAM,WAAY;AAC7C,aAAO,OAAO,cAAc,OAAO,EAAE,IAAI,SAAU,UAAU;AAC3D,eAAO,SAAS,OAAO;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AACD,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACpD;AAEA,SAAO,wBAAwB,SAAS,wBAAwB;AAC9D,WAAO,KAAK,iBAAiB,EAAE,sBAAsB;AAAA,EACvD;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,SAAS;AACzD,WAAO,KAAK,cAAc,MAAM,MAAM,OAAO,EAAE,QAAQ;AAAA,EACzD;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,mBAAmB,SAAS,mBAAmB;AACpD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,oBAAoB,SAAS,kBAAkB,SAAS;AAC7D,SAAK,iBAAiB;AAAA,EACxB;AAEA,SAAO,mBAAmB,SAAS,iBAAiB,UAAU,SAAS;AACrE,QAAI,SAAS,KAAK,cAAc,KAAK,SAAU,GAAG;AAChD,aAAO,aAAa,QAAQ,MAAM,aAAa,EAAE,QAAQ;AAAA,IAC3D,CAAC;AAED,QAAI,QAAQ;AACV,aAAO,iBAAiB;AAAA,IAC1B,OAAO;AACL,WAAK,cAAc,KAAK;AAAA,QACtB;AAAA,QACA,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,mBAAmB,SAAS,iBAAiB,UAAU;AAC5D,QAAI;AAEJ,WAAO,YAAY,wBAAwB,KAAK,cAAc,KAAK,SAAU,GAAG;AAC9E,aAAO,gBAAgB,UAAU,EAAE,QAAQ;AAAA,IAC7C,CAAC,MAAM,OAAO,SAAS,sBAAsB,iBAAiB;AAAA,EAChE;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,aAAa,SAAS;AAC9E,QAAI,SAAS,KAAK,iBAAiB,KAAK,SAAU,GAAG;AACnD,aAAO,aAAa,WAAW,MAAM,aAAa,EAAE,WAAW;AAAA,IACjE,CAAC;AAED,QAAI,QAAQ;AACV,aAAO,iBAAiB;AAAA,IAC1B,OAAO;AACL,WAAK,iBAAiB,KAAK;AAAA,QACzB;AAAA,QACA,gBAAgB;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,aAAa;AACrE,QAAI;AAEJ,WAAO,eAAe,wBAAwB,KAAK,iBAAiB,KAAK,SAAU,GAAG;AACpF,aAAO,gBAAgB,aAAa,EAAE,WAAW;AAAA,IACnD,CAAC,MAAM,OAAO,SAAS,sBAAsB,iBAAiB;AAAA,EAChE;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,SAAS;AACjE,QAAI,WAAW,OAAO,SAAS,QAAQ,YAAY;AACjD,aAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,SAAS,CAAC,GAAG,KAAK,eAAe,SAAS,KAAK,iBAAiB,WAAW,OAAO,SAAS,QAAQ,QAAQ,GAAG,SAAS;AAAA,MAC5I,YAAY;AAAA,IACd,CAAC;AAED,QAAI,CAAC,iBAAiB,aAAa,iBAAiB,UAAU;AAC5D,uBAAiB,YAAY,sBAAsB,iBAAiB,UAAU,gBAAgB;AAAA,IAChG;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,8BAA8B,SAAS,4BAA4B,SAAS;AACjF,WAAO,KAAK,oBAAoB,OAAO;AAAA,EACzC;AAEA,SAAO,yBAAyB,SAAS,uBAAuB,SAAS;AACvE,QAAI,WAAW,OAAO,SAAS,QAAQ,YAAY;AACjD,aAAO;AAAA,IACT;AAEA,WAAO,SAAS,CAAC,GAAG,KAAK,eAAe,WAAW,KAAK,oBAAoB,WAAW,OAAO,SAAS,QAAQ,WAAW,GAAG,SAAS;AAAA,MACpI,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,SAAK,WAAW,MAAM;AACtB,SAAK,cAAc,MAAM;AAAA,EAC3B;AAEA,SAAOA;AACT,EAAE;;;AI7VF;AAQO,IAAI,gBAA6B,SAAU,eAAe;AAC/D,iBAAeC,gBAAe,aAAa;AAE3C,WAASA,eAAc,QAAQ,SAAS;AACtC,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AACpC,UAAM,SAAS;AACf,UAAM,UAAU;AAChB,UAAM,eAAe,CAAC;AACtB,UAAM,cAAc;AAEpB,UAAM,YAAY;AAElB,UAAM,WAAW,OAAO;AAExB,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,eAAc;AAE3B,SAAO,cAAc,SAAS,cAAc;AAC1C,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,EACvC;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,aAAa,YAAY,IAAI;AAElC,UAAI,mBAAmB,KAAK,cAAc,KAAK,OAAO,GAAG;AACvD,aAAK,aAAa;AAAA,MACpB;AAEA,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,SAAO,yBAAyB,SAAS,yBAAyB;AAChE,WAAO,cAAc,KAAK,cAAc,KAAK,SAAS,KAAK,QAAQ,kBAAkB;AAAA,EACvF;AAEA,SAAO,2BAA2B,SAAS,2BAA2B;AACpE,WAAO,cAAc,KAAK,cAAc,KAAK,SAAS,KAAK,QAAQ,oBAAoB;AAAA,EACzF;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY;AACjB,SAAK,aAAa,eAAe,IAAI;AAAA,EACvC;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS,eAAe;AAC9D,QAAI,cAAc,KAAK;AACvB,QAAI,YAAY,KAAK;AACrB,SAAK,UAAU,KAAK,OAAO,4BAA4B,OAAO;AAE9D,QAAI,OAAO,KAAK,QAAQ,YAAY,eAAe,OAAO,KAAK,QAAQ,YAAY,WAAW;AAC5F,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AAGA,QAAI,CAAC,KAAK,QAAQ,UAAU;AAC1B,WAAK,QAAQ,WAAW,YAAY;AAAA,IACtC;AAEA,SAAK,YAAY;AACjB,QAAI,UAAU,KAAK,aAAa;AAEhC,QAAI,WAAW,sBAAsB,KAAK,cAAc,WAAW,KAAK,SAAS,WAAW,GAAG;AAC7F,WAAK,aAAa;AAAA,IACpB;AAGA,SAAK,aAAa,aAAa;AAE/B,QAAI,YAAY,KAAK,iBAAiB,aAAa,KAAK,QAAQ,YAAY,YAAY,WAAW,KAAK,QAAQ,cAAc,YAAY,YAAY;AACpJ,WAAK,mBAAmB;AAAA,IAC1B;AAEA,QAAI,sBAAsB,KAAK,uBAAuB;AAEtD,QAAI,YAAY,KAAK,iBAAiB,aAAa,KAAK,QAAQ,YAAY,YAAY,WAAW,wBAAwB,KAAK,yBAAyB;AACvJ,WAAK,sBAAsB,mBAAmB;AAAA,IAChD;AAAA,EACF;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,SAAS;AACjE,QAAI,mBAAmB,KAAK,OAAO,4BAA4B,OAAO;AACtE,QAAI,QAAQ,KAAK,OAAO,cAAc,EAAE,MAAM,KAAK,QAAQ,gBAAgB;AAC3E,WAAO,KAAK,aAAa,OAAO,gBAAgB;AAAA,EAClD;AAEA,SAAO,mBAAmB,SAAS,mBAAmB;AACpD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,cAAc,SAAS,YAAY,QAAQ,kBAAkB;AAClE,QAAI,SAAS;AAEb,QAAI,gBAAgB,CAAC;AAErB,QAAI,YAAY,SAASC,WAAU,KAAK;AACtC,UAAI,CAAC,OAAO,aAAa,SAAS,GAAG,GAAG;AACtC,eAAO,aAAa,KAAK,GAAG;AAAA,MAC9B;AAAA,IACF;AAEA,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AACzC,aAAO,eAAe,eAAe,KAAK;AAAA,QACxC,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,KAAK,SAAS,MAAM;AAClB,oBAAU,GAAG;AACb,iBAAO,OAAO,GAAG;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,QAAI,iBAAiB,oBAAoB,iBAAiB,UAAU;AAClE,gBAAU,OAAO;AAAA,IACnB;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,gBAAgB,SAAS,cAAc,SAAS;AACrD,QAAI,SAAS;AAEb,WAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC5C,UAAI,cAAc,OAAO,UAAU,SAAU,QAAQ;AACnD,YAAI,CAAC,OAAO,YAAY;AACtB,sBAAY;AAEZ,cAAI,OAAO,YAAY,WAAW,OAAO,SAAS,QAAQ,eAAe;AACvE,mBAAO,OAAO,KAAK;AAAA,UACrB,OAAO;AACL,oBAAQ,MAAM;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAO,kBAAkB,SAAS,kBAAkB;AAClD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,SAAK,OAAO,cAAc,EAAE,OAAO,KAAK,YAAY;AAAA,EACtD;AAEA,SAAO,UAAU,SAAS,QAAQ,SAAS;AACzC,WAAO,KAAK,MAAM,SAAS,CAAC,GAAG,SAAS;AAAA,MACtC,MAAM;AAAA,QACJ,aAAa,WAAW,OAAO,SAAS,QAAQ;AAAA,MAClD;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,SAAS;AACzD,QAAI,SAAS;AAEb,QAAI,mBAAmB,KAAK,OAAO,4BAA4B,OAAO;AACtE,QAAI,QAAQ,KAAK,OAAO,cAAc,EAAE,MAAM,KAAK,QAAQ,gBAAgB;AAC3E,WAAO,MAAM,MAAM,EAAE,KAAK,WAAY;AACpC,aAAO,OAAO,aAAa,OAAO,gBAAgB;AAAA,IACpD,CAAC;AAAA,EACH;AAEA,SAAO,QAAQ,SAAS,MAAM,cAAc;AAC1C,QAAI,SAAS;AAEb,WAAO,KAAK,aAAa,YAAY,EAAE,KAAK,WAAY;AACtD,aAAO,aAAa;AAEpB,aAAO,OAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,SAAO,eAAe,SAAS,aAAa,cAAc;AAExD,SAAK,YAAY;AAEjB,QAAI,UAAU,KAAK,aAAa,MAAM,KAAK,SAAS,YAAY;AAEhE,QAAI,EAAE,gBAAgB,OAAO,SAAS,aAAa,eAAe;AAChE,gBAAU,QAAQ,MAAM,IAAI;AAAA,IAC9B;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,qBAAqB,SAAS,qBAAqB;AACxD,QAAI,SAAS;AAEb,SAAK,kBAAkB;AAEvB,QAAI,YAAY,KAAK,cAAc,WAAW,CAAC,eAAe,KAAK,QAAQ,SAAS,GAAG;AACrF;AAAA,IACF;AAEA,QAAI,OAAO,eAAe,KAAK,cAAc,eAAe,KAAK,QAAQ,SAAS;AAGlF,QAAI,UAAU,OAAO;AACrB,SAAK,iBAAiB,WAAW,WAAY;AAC3C,UAAI,CAAC,OAAO,cAAc,SAAS;AACjC,eAAO,aAAa;AAAA,MACtB;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAEA,SAAO,yBAAyB,SAAS,yBAAyB;AAChE,QAAI;AAEJ,WAAO,OAAO,KAAK,QAAQ,oBAAoB,aAAa,KAAK,QAAQ,gBAAgB,KAAK,cAAc,MAAM,KAAK,YAAY,KAAK,wBAAwB,KAAK,QAAQ,oBAAoB,OAAO,wBAAwB;AAAA,EAClO;AAEA,SAAO,wBAAwB,SAAS,sBAAsB,cAAc;AAC1E,QAAI,SAAS;AAEb,SAAK,qBAAqB;AAC1B,SAAK,yBAAyB;AAE9B,QAAI,YAAY,KAAK,QAAQ,YAAY,SAAS,CAAC,eAAe,KAAK,sBAAsB,KAAK,KAAK,2BAA2B,GAAG;AACnI;AAAA,IACF;AAEA,SAAK,oBAAoB,YAAY,WAAY;AAC/C,UAAI,OAAO,QAAQ,+BAA+B,aAAa,UAAU,GAAG;AAC1E,eAAO,aAAa;AAAA,MACtB;AAAA,IACF,GAAG,KAAK,sBAAsB;AAAA,EAChC;AAEA,SAAO,eAAe,SAAS,eAAe;AAC5C,SAAK,mBAAmB;AACxB,SAAK,sBAAsB,KAAK,uBAAuB,CAAC;AAAA,EAC1D;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,SAAK,kBAAkB;AACvB,SAAK,qBAAqB;AAAA,EAC5B;AAEA,SAAO,oBAAoB,SAAS,oBAAoB;AACtD,QAAI,KAAK,gBAAgB;AACvB,mBAAa,KAAK,cAAc;AAChC,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAEA,SAAO,uBAAuB,SAAS,uBAAuB;AAC5D,QAAI,KAAK,mBAAmB;AAC1B,oBAAc,KAAK,iBAAiB;AACpC,WAAK,oBAAoB;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO,eAAe,SAAS,aAAa,OAAO,SAAS;AAC1D,QAAI,YAAY,KAAK;AACrB,QAAI,cAAc,KAAK;AACvB,QAAI,aAAa,KAAK;AACtB,QAAI,kBAAkB,KAAK;AAC3B,QAAI,oBAAoB,KAAK;AAC7B,QAAI,cAAc,UAAU;AAC5B,QAAI,oBAAoB,cAAc,MAAM,QAAQ,KAAK;AACzD,QAAI,kBAAkB,cAAc,KAAK,gBAAgB,KAAK;AAC9D,QAAI,QAAQ,MAAM;AAClB,QAAI,gBAAgB,MAAM,eACtB,QAAQ,MAAM,OACd,iBAAiB,MAAM,gBACvB,aAAa,MAAM,YACnB,SAAS,MAAM;AACnB,QAAI,iBAAiB;AACrB,QAAI,oBAAoB;AACxB,QAAI;AAEJ,QAAI,QAAQ,mBAAmB;AAC7B,UAAI,UAAU,KAAK,aAAa;AAChC,UAAI,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;AAChE,UAAI,kBAAkB,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;AAE7F,UAAI,gBAAgB,iBAAiB;AACnC,qBAAa;AAEb,YAAI,CAAC,eAAe;AAClB,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAGA,QAAI,QAAQ,oBAAoB,CAAC,MAAM,oBAAoB,mBAAmB,OAAO,SAAS,gBAAgB,cAAc,WAAW,SAAS;AAC9I,aAAO,gBAAgB;AACvB,sBAAgB,gBAAgB;AAChC,eAAS,gBAAgB;AACzB,uBAAiB;AAAA,IACnB,WACS,QAAQ,UAAU,OAAO,MAAM,SAAS,aAAa;AAE1D,UAAI,cAAc,MAAM,UAAU,mBAAmB,OAAO,SAAS,gBAAgB,SAAS,QAAQ,WAAW,KAAK,UAAU;AAC9H,eAAO,KAAK;AAAA,MACd,OAAO;AACL,YAAI;AACF,eAAK,WAAW,QAAQ;AACxB,iBAAO,QAAQ,OAAO,MAAM,IAAI;AAEhC,cAAI,QAAQ,sBAAsB,OAAO;AACvC,mBAAO,iBAAiB,cAAc,OAAO,SAAS,WAAW,MAAM,IAAI;AAAA,UAC7E;AAEA,eAAK,eAAe;AACpB,eAAK,cAAc;AAAA,QACrB,SAAS,aAAa;AACpB,oBAAU,EAAE,MAAM,WAAW;AAC7B,eAAK,cAAc;AAAA,QACrB;AAAA,MACF;AAAA,IACF,OACK;AACD,aAAO,MAAM;AAAA,IACf;AAGJ,QAAI,OAAO,QAAQ,oBAAoB,eAAe,OAAO,SAAS,gBAAgB,WAAW,aAAa,WAAW,SAAS;AAChI,UAAI;AAEJ,WAAK,cAAc,OAAO,SAAS,WAAW,sBAAsB,QAAQ,qBAAqB,qBAAqB,OAAO,SAAS,kBAAkB,kBAAkB;AACxK,0BAAkB,WAAW;AAAA,MAC/B,OAAO;AACL,0BAAkB,OAAO,QAAQ,oBAAoB,aAAa,QAAQ,gBAAgB,IAAI,QAAQ;AAEtG,YAAI,QAAQ,UAAU,OAAO,oBAAoB,aAAa;AAC5D,cAAI;AACF,8BAAkB,QAAQ,OAAO,eAAe;AAEhD,gBAAI,QAAQ,sBAAsB,OAAO;AACvC,gCAAkB,iBAAiB,cAAc,OAAO,SAAS,WAAW,MAAM,eAAe;AAAA,YACnG;AAEA,iBAAK,cAAc;AAAA,UACrB,SAAS,aAAa;AACpB,sBAAU,EAAE,MAAM,WAAW;AAC7B,iBAAK,cAAc;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAEA,UAAI,OAAO,oBAAoB,aAAa;AAC1C,iBAAS;AACT,eAAO;AACP,4BAAoB;AAAA,MACtB;AAAA,IACF;AAEA,QAAI,KAAK,aAAa;AACpB,cAAQ,KAAK;AACb,aAAO,KAAK;AACZ,uBAAiB,KAAK,IAAI;AAC1B,eAAS;AAAA,IACX;AAEA,QAAI,SAAS;AAAA,MACX;AAAA,MACA,WAAW,WAAW;AAAA,MACtB,WAAW,WAAW;AAAA,MACtB,SAAS,WAAW;AAAA,MACpB,QAAQ,WAAW;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,MAAM;AAAA,MACpB,kBAAkB,MAAM;AAAA,MACxB,WAAW,MAAM,kBAAkB,KAAK,MAAM,mBAAmB;AAAA,MACjE,qBAAqB,MAAM,kBAAkB,kBAAkB,mBAAmB,MAAM,mBAAmB,kBAAkB;AAAA,MAC7H;AAAA,MACA,cAAc,cAAc,WAAW;AAAA,MACvC,gBAAgB,WAAW,WAAW,MAAM,kBAAkB;AAAA,MAC9D;AAAA,MACA;AAAA,MACA,gBAAgB,WAAW,WAAW,MAAM,kBAAkB;AAAA,MAC9D,SAAS,QAAQ,OAAO,OAAO;AAAA,MAC/B,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAEA,SAAO,wBAAwB,SAAS,sBAAsB,QAAQ,YAAY;AAChF,QAAI,CAAC,YAAY;AACf,aAAO;AAAA,IACT;AAEA,QAAI,gBAAgB,KAAK,SACrB,sBAAsB,cAAc,qBACpC,gCAAgC,cAAc;AAElD,QAAI,CAAC,uBAAuB,CAAC,+BAA+B;AAC1D,aAAO;AAAA,IACT;AAEA,QAAI,wBAAwB,aAAa,CAAC,KAAK,aAAa,QAAQ;AAClE,aAAO;AAAA,IACT;AAEA,QAAI,gBAAgB,wBAAwB,YAAY,KAAK,eAAe;AAC5E,WAAO,OAAO,KAAK,MAAM,EAAE,KAAK,SAAU,KAAK;AAC7C,UAAI,WAAW;AACf,UAAI,UAAU,OAAO,QAAQ,MAAM,WAAW,QAAQ;AACtD,UAAI,aAAa,iBAAiB,OAAO,SAAS,cAAc,KAAK,SAAU,GAAG;AAChF,eAAO,MAAM;AAAA,MACf,CAAC;AACD,UAAI,aAAa,iCAAiC,OAAO,SAAS,8BAA8B,KAAK,SAAU,GAAG;AAChH,eAAO,MAAM;AAAA,MACf,CAAC;AACD,aAAO,WAAW,CAAC,eAAe,CAAC,iBAAiB;AAAA,IACtD,CAAC;AAAA,EACH;AAEA,SAAO,eAAe,SAAS,aAAa,eAAe;AACzD,QAAI,aAAa,KAAK;AACtB,SAAK,gBAAgB,KAAK,aAAa,KAAK,cAAc,KAAK,OAAO;AACtE,SAAK,qBAAqB,KAAK,aAAa;AAC5C,SAAK,uBAAuB,KAAK;AAEjC,QAAI,oBAAoB,KAAK,eAAe,UAAU,GAAG;AACvD;AAAA,IACF;AAGA,QAAI,uBAAuB;AAAA,MACzB,OAAO;AAAA,IACT;AAEA,SAAK,iBAAiB,OAAO,SAAS,cAAc,eAAe,SAAS,KAAK,sBAAsB,KAAK,eAAe,UAAU,GAAG;AACtI,2BAAqB,YAAY;AAAA,IACnC;AAEA,SAAK,OAAO,SAAS,CAAC,GAAG,sBAAsB,aAAa,CAAC;AAAA,EAC/D;AAEA,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,QAAQ,KAAK,OAAO,cAAc,EAAE,MAAM,KAAK,QAAQ,KAAK,OAAO;AAEvE,QAAI,UAAU,KAAK,cAAc;AAC/B;AAAA,IACF;AAEA,QAAI,YAAY,KAAK;AACrB,SAAK,eAAe;AACpB,SAAK,2BAA2B,MAAM;AACtC,SAAK,sBAAsB,KAAK;AAEhC,QAAI,KAAK,aAAa,GAAG;AACvB,mBAAa,OAAO,SAAS,UAAU,eAAe,IAAI;AAC1D,YAAM,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,cAAc,QAAQ;AACpD,QAAI,gBAAgB,CAAC;AAErB,QAAI,OAAO,SAAS,WAAW;AAC7B,oBAAc,YAAY;AAAA,IAC5B,WAAW,OAAO,SAAS,WAAW,CAAC,iBAAiB,OAAO,KAAK,GAAG;AACrE,oBAAc,UAAU;AAAA,IAC1B;AAEA,SAAK,aAAa,aAAa;AAE/B,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,OAAO,eAAe;AAC7C,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAE9B,UAAI,cAAc,WAAW;AAC3B,eAAO,QAAQ,aAAa,OAAO,SAAS,OAAO,QAAQ,UAAU,OAAO,cAAc,IAAI;AAC9F,eAAO,QAAQ,aAAa,OAAO,SAAS,OAAO,QAAQ,UAAU,OAAO,cAAc,MAAM,IAAI;AAAA,MACtG,WAAW,cAAc,SAAS;AAChC,eAAO,QAAQ,WAAW,OAAO,SAAS,OAAO,QAAQ,QAAQ,OAAO,cAAc,KAAK;AAC3F,eAAO,QAAQ,aAAa,OAAO,SAAS,OAAO,QAAQ,UAAU,QAAW,OAAO,cAAc,KAAK;AAAA,MAC5G;AAGA,UAAI,cAAc,WAAW;AAC3B,eAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,mBAAS,OAAO,aAAa;AAAA,QAC/B,CAAC;AAAA,MACH;AAGA,UAAI,cAAc,OAAO;AACvB,eAAO,OAAO,cAAc,EAAE,OAAO;AAAA,UACnC,OAAO,OAAO;AAAA,UACd,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAOD;AACT,EAAE,YAAY;AAEd,SAAS,kBAAkB,OAAO,SAAS;AACzC,SAAO,QAAQ,YAAY,SAAS,CAAC,MAAM,MAAM,iBAAiB,EAAE,MAAM,MAAM,WAAW,WAAW,QAAQ,iBAAiB;AACjI;AAEA,SAAS,mBAAmB,OAAO,SAAS;AAC1C,SAAO,kBAAkB,OAAO,OAAO,KAAK,MAAM,MAAM,gBAAgB,KAAK,cAAc,OAAO,SAAS,QAAQ,cAAc;AACnI;AAEA,SAAS,cAAc,OAAO,SAAS,OAAO;AAC5C,MAAI,QAAQ,YAAY,OAAO;AAC7B,QAAI,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACzD,WAAO,UAAU,YAAY,UAAU,SAAS,QAAQ,OAAO,OAAO;AAAA,EACxE;AAEA,SAAO;AACT;AAEA,SAAS,sBAAsB,OAAO,WAAW,SAAS,aAAa;AACrE,SAAO,QAAQ,YAAY,UAAU,UAAU,aAAa,YAAY,YAAY,WAAW,CAAC,QAAQ,YAAY,MAAM,MAAM,WAAW,YAAY,QAAQ,OAAO,OAAO;AAC/K;AAEA,SAAS,QAAQ,OAAO,SAAS;AAC/B,SAAO,MAAM,cAAc,QAAQ,SAAS;AAC9C;;;AC/hBO,IAAI,kBAA+B,SAAU,eAAe;AACjE,iBAAeE,kBAAiB,aAAa;AAE7C,WAASA,iBAAgB,QAAQ,SAAS;AACxC,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AACpC,UAAM,SAAS;AACf,UAAM,UAAU,CAAC;AACjB,UAAM,SAAS,CAAC;AAChB,UAAM,YAAY,CAAC;AACnB,UAAM,eAAe,CAAC;AAEtB,QAAI,SAAS;AACX,YAAM,WAAW,OAAO;AAAA,IAC1B;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,iBAAgB;AAE7B,SAAO,cAAc,SAAS,cAAc;AAC1C,QAAI,SAAS;AAEb,QAAI,KAAK,UAAU,WAAW,GAAG;AAC/B,WAAK,UAAU,QAAQ,SAAU,UAAU;AACzC,iBAAS,UAAU,SAAU,QAAQ;AACnC,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,QAAQ,SAAU,UAAU;AACzC,eAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS,eAAe;AAC9D,SAAK,UAAU;AACf,SAAK,gBAAgB,aAAa;AAAA,EACpC;AAEA,SAAO,mBAAmB,SAAS,mBAAmB;AACpD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,SAAS;AACjE,WAAO,KAAK,sBAAsB,OAAO,EAAE,IAAI,SAAU,OAAO;AAC9D,aAAO,MAAM,SAAS,oBAAoB,MAAM,qBAAqB;AAAA,IACvE,CAAC;AAAA,EACH;AAEA,SAAO,wBAAwB,SAAS,sBAAsB,SAAS;AACrE,QAAI,SAAS;AAEb,QAAI,gBAAgB,KAAK;AACzB,QAAI,wBAAwB,QAAQ,IAAI,SAAU,SAAS;AACzD,aAAO,OAAO,OAAO,4BAA4B,OAAO;AAAA,IAC1D,CAAC;AACD,QAAI,oBAAoB,sBAAsB,QAAQ,SAAU,kBAAkB;AAChF,UAAI,QAAQ,cAAc,KAAK,SAAU,UAAU;AACjD,eAAO,SAAS,QAAQ,cAAc,iBAAiB;AAAA,MACzD,CAAC;AAED,UAAI,SAAS,MAAM;AACjB,eAAO,CAAC;AAAA,UACN,uBAAuB;AAAA,UACvB,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAEA,aAAO,CAAC;AAAA,IACV,CAAC;AACD,QAAI,qBAAqB,kBAAkB,IAAI,SAAU,OAAO;AAC9D,aAAO,MAAM,sBAAsB;AAAA,IACrC,CAAC;AACD,QAAI,mBAAmB,sBAAsB,OAAO,SAAU,kBAAkB;AAC9E,aAAO,CAAC,mBAAmB,SAAS,iBAAiB,SAAS;AAAA,IAChE,CAAC;AACD,QAAI,qBAAqB,cAAc,OAAO,SAAU,cAAc;AACpE,aAAO,CAAC,kBAAkB,KAAK,SAAU,OAAO;AAC9C,eAAO,MAAM,aAAa;AAAA,MAC5B,CAAC;AAAA,IACH,CAAC;AACD,QAAI,uBAAuB,iBAAiB,IAAI,SAAU,SAAS,OAAO;AACxE,UAAI,QAAQ,kBAAkB;AAE5B,YAAI,yBAAyB,mBAAmB,KAAK;AAErD,YAAI,2BAA2B,QAAW;AACxC,iBAAO;AAAA,YACL,uBAAuB;AAAA,YACvB,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,uBAAuB;AAAA,QACvB,UAAU,OAAO,YAAY,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AAED,QAAI,8BAA8B,SAASC,6BAA4B,GAAG,GAAG;AAC3E,aAAO,sBAAsB,QAAQ,EAAE,qBAAqB,IAAI,sBAAsB,QAAQ,EAAE,qBAAqB;AAAA,IACvH;AAEA,WAAO,kBAAkB,OAAO,oBAAoB,EAAE,KAAK,2BAA2B;AAAA,EACxF;AAEA,SAAO,cAAc,SAAS,YAAY,SAAS;AACjD,QAAI,mBAAmB,KAAK,OAAO,4BAA4B,OAAO;AACtE,QAAI,kBAAkB,KAAK,aAAa,iBAAiB,SAAS;AAClE,WAAO,mBAAmB,OAAO,kBAAkB,IAAI,cAAc,KAAK,QAAQ,gBAAgB;AAAA,EACpG;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,eAAe;AAC/D,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,UAAI,gBAAgB,OAAO;AAE3B,UAAI,qBAAqB,OAAO,sBAAsB,OAAO,OAAO;AAGpE,yBAAmB,QAAQ,SAAU,OAAO;AAC1C,eAAO,MAAM,SAAS,WAAW,MAAM,uBAAuB,aAAa;AAAA,MAC7E,CAAC;AACD,UAAI,eAAe,mBAAmB,IAAI,SAAU,OAAO;AACzD,eAAO,MAAM;AAAA,MACf,CAAC;AACD,UAAI,kBAAkB,OAAO,YAAY,aAAa,IAAI,SAAU,UAAU;AAC5E,eAAO,CAAC,SAAS,QAAQ,WAAW,QAAQ;AAAA,MAC9C,CAAC,CAAC;AACF,UAAI,YAAY,aAAa,IAAI,SAAU,UAAU;AACnD,eAAO,SAAS,iBAAiB;AAAA,MACnC,CAAC;AACD,UAAI,iBAAiB,aAAa,KAAK,SAAU,UAAU,OAAO;AAChE,eAAO,aAAa,cAAc,KAAK;AAAA,MACzC,CAAC;AAED,UAAI,cAAc,WAAW,aAAa,UAAU,CAAC,gBAAgB;AACnE;AAAA,MACF;AAEA,aAAO,YAAY;AACnB,aAAO,eAAe;AACtB,aAAO,SAAS;AAEhB,UAAI,CAAC,OAAO,aAAa,GAAG;AAC1B;AAAA,MACF;AAEA,iBAAW,eAAe,YAAY,EAAE,QAAQ,SAAU,UAAU;AAClE,iBAAS,QAAQ;AAAA,MACnB,CAAC;AACD,iBAAW,cAAc,aAAa,EAAE,QAAQ,SAAU,UAAU;AAClE,iBAAS,UAAU,SAAU,QAAQ;AACnC,iBAAO,SAAS,UAAU,MAAM;AAAA,QAClC,CAAC;AAAA,MACH,CAAC;AAED,aAAO,OAAO;AAAA,IAChB,CAAC;AAAA,EACH;AAEA,SAAO,WAAW,SAAS,SAAS,UAAU,QAAQ;AACpD,QAAI,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AAE3C,QAAI,UAAU,IAAI;AAChB,WAAK,SAAS,UAAU,KAAK,QAAQ,OAAO,MAAM;AAClD,WAAK,OAAO;AAAA,IACd;AAAA,EACF;AAEA,SAAO,SAAS,SAAS,SAAS;AAChC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAC9B,aAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,iBAAS,OAAO,MAAM;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,SAAOD;AACT,EAAE,YAAY;;;ACzMd;AAIO,IAAI,wBAAqC,SAAU,gBAAgB;AACxE,iBAAeE,wBAAuB,cAAc;AAMpD,WAASA,uBAAsB,QAAQ,SAAS;AAC9C,WAAO,eAAe,KAAK,MAAM,QAAQ,OAAO,KAAK;AAAA,EACvD;AAEA,MAAI,SAASA,uBAAsB;AAEnC,SAAO,cAAc,SAAS,cAAc;AAC1C,mBAAe,UAAU,YAAY,KAAK,IAAI;AAE9C,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC3D;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS,eAAe;AAC9D,mBAAe,UAAU,WAAW,KAAK,MAAM,SAAS,CAAC,GAAG,SAAS;AAAA,MACnE,UAAU,sBAAsB;AAAA,IAClC,CAAC,GAAG,aAAa;AAAA,EACnB;AAEA,SAAO,sBAAsB,SAAS,oBAAoB,SAAS;AACjE,YAAQ,WAAW,sBAAsB;AACzC,WAAO,eAAe,UAAU,oBAAoB,KAAK,MAAM,OAAO;AAAA,EACxE;AAEA,SAAO,gBAAgB,SAAS,cAAc,SAAS;AACrD,QAAI;AAEJ,WAAO,KAAK,MAAM;AAAA;AAAA,MAEhB,gBAAgB,wBAAwB,WAAW,OAAO,SAAS,QAAQ,kBAAkB,OAAO,wBAAwB;AAAA,MAC5H,cAAc,WAAW,OAAO,SAAS,QAAQ;AAAA,MACjD,MAAM;AAAA,QACJ,WAAW;AAAA,UACT,WAAW;AAAA,UACX,WAAW,WAAW,OAAO,SAAS,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,oBAAoB,SAAS,kBAAkB,SAAS;AAC7D,QAAI;AAEJ,WAAO,KAAK,MAAM;AAAA;AAAA,MAEhB,gBAAgB,yBAAyB,WAAW,OAAO,SAAS,QAAQ,kBAAkB,OAAO,yBAAyB;AAAA,MAC9H,cAAc,WAAW,OAAO,SAAS,QAAQ;AAAA,MACjD,MAAM;AAAA,QACJ,WAAW;AAAA,UACT,WAAW;AAAA,UACX,WAAW,WAAW,OAAO,SAAS,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,eAAe,SAAS,aAAa,OAAO,SAAS;AAC1D,QAAI,aAAa,cAAc,kBAAkB,uBAAuB,mBAAmB;AAE3F,QAAI,QAAQ,MAAM;AAElB,QAAI,SAAS,eAAe,UAAU,aAAa,KAAK,MAAM,OAAO,OAAO;AAE5E,WAAO,SAAS,CAAC,GAAG,QAAQ;AAAA,MAC1B,eAAe,KAAK;AAAA,MACpB,mBAAmB,KAAK;AAAA,MACxB,aAAa,YAAY,UAAU,cAAc,MAAM,SAAS,OAAO,SAAS,YAAY,KAAK;AAAA,MACjG,iBAAiB,gBAAgB,UAAU,eAAe,MAAM,SAAS,OAAO,SAAS,aAAa,KAAK;AAAA,MAC3G,oBAAoB,MAAM,gBAAgB,mBAAmB,MAAM,cAAc,OAAO,UAAU,wBAAwB,iBAAiB,cAAc,OAAO,SAAS,sBAAsB,eAAe;AAAA,MAC9M,wBAAwB,MAAM,gBAAgB,oBAAoB,MAAM,cAAc,OAAO,UAAU,wBAAwB,kBAAkB,cAAc,OAAO,SAAS,sBAAsB,eAAe;AAAA,IACtN,CAAC;AAAA,EACH;AAEA,SAAOA;AACT,EAAE,aAAa;;;ACrFf;AAMO,IAAI,mBAAgC,SAAU,eAAe;AAClE,iBAAeC,mBAAkB,aAAa;AAE9C,WAASA,kBAAiB,QAAQ,SAAS;AACzC,QAAI;AAEJ,YAAQ,cAAc,KAAK,IAAI,KAAK;AACpC,UAAM,SAAS;AAEf,UAAM,WAAW,OAAO;AAExB,UAAM,YAAY;AAElB,UAAM,aAAa;AAEnB,WAAO;AAAA,EACT;AAEA,MAAI,SAASA,kBAAiB;AAE9B,SAAO,cAAc,SAAS,cAAc;AAC1C,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AAEA,SAAO,aAAa,SAAS,WAAW,SAAS;AAC/C,SAAK,UAAU,KAAK,OAAO,uBAAuB,OAAO;AAAA,EAC3D;AAEA,SAAO,gBAAgB,SAAS,gBAAgB;AAC9C,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,UAAI;AAEJ,OAAC,wBAAwB,KAAK,oBAAoB,OAAO,SAAS,sBAAsB,eAAe,IAAI;AAAA,IAC7G;AAAA,EACF;AAEA,SAAO,mBAAmB,SAAS,iBAAiB,QAAQ;AAC1D,SAAK,aAAa;AAElB,QAAI,gBAAgB;AAAA,MAClB,WAAW;AAAA,IACb;AAEA,QAAI,OAAO,SAAS,WAAW;AAC7B,oBAAc,YAAY;AAAA,IAC5B,WAAW,OAAO,SAAS,SAAS;AAClC,oBAAc,UAAU;AAAA,IAC1B;AAEA,SAAK,OAAO,aAAa;AAAA,EAC3B;AAEA,SAAO,mBAAmB,SAAS,mBAAmB;AACpD,WAAO,KAAK;AAAA,EACd;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,OAAO;AAAA,MACV,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAEA,SAAO,SAAS,SAAS,OAAO,WAAW,SAAS;AAClD,SAAK,gBAAgB;AAErB,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,eAAe,IAAI;AAAA,IAC1C;AAEA,SAAK,kBAAkB,KAAK,OAAO,iBAAiB,EAAE,MAAM,KAAK,QAAQ,SAAS,CAAC,GAAG,KAAK,SAAS;AAAA,MAClG,WAAW,OAAO,cAAc,cAAc,YAAY,KAAK,QAAQ;AAAA,IACzE,CAAC,CAAC;AACF,SAAK,gBAAgB,YAAY,IAAI;AACrC,WAAO,KAAK,gBAAgB,QAAQ;AAAA,EACtC;AAEA,SAAO,eAAe,SAAS,eAAe;AAC5C,QAAI,QAAQ,KAAK,kBAAkB,KAAK,gBAAgB,QAAQ,gBAAgB;AAEhF,QAAI,SAAS,SAAS,CAAC,GAAG,OAAO;AAAA,MAC/B,WAAW,MAAM,WAAW;AAAA,MAC5B,WAAW,MAAM,WAAW;AAAA,MAC5B,SAAS,MAAM,WAAW;AAAA,MAC1B,QAAQ,MAAM,WAAW;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd,CAAC;AAED,SAAK,gBAAgB;AAAA,EACvB;AAEA,SAAO,SAAS,SAAS,OAAO,SAAS;AACvC,QAAI,SAAS;AAEb,kBAAc,MAAM,WAAY;AAE9B,UAAI,OAAO,eAAe;AACxB,YAAI,QAAQ,WAAW;AACrB,iBAAO,cAAc,aAAa,OAAO,SAAS,OAAO,cAAc,UAAU,OAAO,cAAc,MAAM,OAAO,cAAc,WAAW,OAAO,cAAc,OAAO;AACxK,iBAAO,cAAc,aAAa,OAAO,SAAS,OAAO,cAAc,UAAU,OAAO,cAAc,MAAM,MAAM,OAAO,cAAc,WAAW,OAAO,cAAc,OAAO;AAAA,QAChL,WAAW,QAAQ,SAAS;AAC1B,iBAAO,cAAc,WAAW,OAAO,SAAS,OAAO,cAAc,QAAQ,OAAO,cAAc,OAAO,OAAO,cAAc,WAAW,OAAO,cAAc,OAAO;AACrK,iBAAO,cAAc,aAAa,OAAO,SAAS,OAAO,cAAc,UAAU,QAAW,OAAO,cAAc,OAAO,OAAO,cAAc,WAAW,OAAO,cAAc,OAAO;AAAA,QACtL;AAAA,MACF;AAGA,UAAI,QAAQ,WAAW;AACrB,eAAO,UAAU,QAAQ,SAAU,UAAU;AAC3C,mBAAS,OAAO,aAAa;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAOA;AACT,EAAE,YAAY;;;AC7Hd;AAIA,SAAS,kBAAkB,UAAU;AACnC,SAAO;AAAA,IACL,aAAa,SAAS,QAAQ;AAAA,IAC9B,OAAO,SAAS;AAAA,EAClB;AACF;AAMA,SAAS,eAAe,OAAO;AAC7B,SAAO;AAAA,IACL,OAAO,MAAM;AAAA,IACb,UAAU,MAAM;AAAA,IAChB,WAAW,MAAM;AAAA,EACnB;AACF;AAEA,SAAS,+BAA+B,UAAU;AAChD,SAAO,SAAS,MAAM;AACxB;AAEA,SAAS,4BAA4B,OAAO;AAC1C,SAAO,MAAM,MAAM,WAAW;AAChC;AAEO,SAAS,UAAU,QAAQ,SAAS;AACzC,MAAI,UAAU;AAEd,YAAU,WAAW,CAAC;AACtB,MAAI,YAAY,CAAC;AACjB,MAAI,UAAU,CAAC;AAEf,QAAM,WAAW,YAAY,OAAO,SAAS,SAAS,wBAAwB,OAAO;AACnF,QAAI,0BAA0B,QAAQ,2BAA2B;AACjE,WAAO,iBAAiB,EAAE,OAAO,EAAE,QAAQ,SAAU,UAAU;AAC7D,UAAI,wBAAwB,QAAQ,GAAG;AACrC,kBAAU,KAAK,kBAAkB,QAAQ,CAAC;AAAA,MAC5C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,QAAM,YAAY,YAAY,OAAO,SAAS,UAAU,sBAAsB,OAAO;AACnF,QAAI,uBAAuB,QAAQ,wBAAwB;AAC3D,WAAO,cAAc,EAAE,OAAO,EAAE,QAAQ,SAAU,OAAO;AACvD,UAAI,qBAAqB,KAAK,GAAG;AAC/B,gBAAQ,KAAK,eAAe,KAAK,CAAC;AAAA,MACpC;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACO,SAAS,QAAQ,QAAQ,iBAAiB,SAAS;AACxD,MAAI,OAAO,oBAAoB,YAAY,oBAAoB,MAAM;AACnE;AAAA,EACF;AAEA,MAAI,gBAAgB,OAAO,iBAAiB;AAC5C,MAAI,aAAa,OAAO,cAAc;AACtC,MAAI,YAAY,gBAAgB,aAAa,CAAC;AAC9C,MAAI,UAAU,gBAAgB,WAAW,CAAC;AAC1C,YAAU,QAAQ,SAAU,oBAAoB;AAC9C,QAAI;AAEJ,kBAAc,MAAM,QAAQ,SAAS,CAAC,GAAG,WAAW,OAAO,UAAU,wBAAwB,QAAQ,mBAAmB,OAAO,SAAS,sBAAsB,WAAW;AAAA,MACvK,aAAa,mBAAmB;AAAA,IAClC,CAAC,GAAG,mBAAmB,KAAK;AAAA,EAC9B,CAAC;AACD,UAAQ,QAAQ,SAAU,iBAAiB;AACzC,QAAI;AAEJ,QAAI,QAAQ,WAAW,IAAI,gBAAgB,SAAS;AAEpD,QAAI,OAAO;AACT,UAAI,MAAM,MAAM,gBAAgB,gBAAgB,MAAM,eAAe;AACnE,cAAM,SAAS,gBAAgB,KAAK;AAAA,MACtC;AAEA;AAAA,IACF;AAGA,eAAW,MAAM,QAAQ,SAAS,CAAC,GAAG,WAAW,OAAO,UAAU,yBAAyB,QAAQ,mBAAmB,OAAO,SAAS,uBAAuB,SAAS;AAAA,MACpK,UAAU,gBAAgB;AAAA,MAC1B,WAAW,gBAAgB;AAAA,IAC7B,CAAC,GAAG,gBAAgB,KAAK;AAAA,EAC3B,CAAC;AACH;;;AChGA,uBAAqB;AACd,IAAI,0BAA0B,iBAAAC,QAAS;;;ACC9C,cAAc,uBAAuB,uBAAuB;;;ACFrD,IAAIC,UAAS;;;ACEpB,UAAUC,OAAM;;;ACFhB,mBAAkB;AAClB,IAAI,iBAA8B,aAAAC,QAAM,cAAc,MAAS;AAC/D,IAAI,4BAAyC,aAAAA,QAAM,cAAc,KAAK;AAOtE,SAAS,sBAAsB,gBAAgB;AAC7C,MAAI,kBAAkB,OAAO,WAAW,aAAa;AACnD,QAAI,CAAC,OAAO,yBAAyB;AACnC,aAAO,0BAA0B;AAAA,IACnC;AAEA,WAAO,OAAO;AAAA,EAChB;AAEA,SAAO;AACT;AAEO,IAAI,iBAAiB,SAASC,kBAAiB;AACpD,MAAI,cAAc,aAAAD,QAAM,WAAW,sBAAsB,aAAAA,QAAM,WAAW,yBAAyB,CAAC,CAAC;AAErG,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AAEA,SAAO;AACT;AACO,IAAI,sBAAsB,SAASE,qBAAoB,MAAM;AAClE,MAAI,SAAS,KAAK,QACd,sBAAsB,KAAK,gBAC3B,iBAAiB,wBAAwB,SAAS,QAAQ,qBAC1D,WAAW,KAAK;AACpB,eAAAF,QAAM,UAAU,WAAY;AAC1B,WAAO,MAAM;AACb,WAAO,WAAY;AACjB,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF,GAAG,CAAC,MAAM,CAAC;AACX,MAAI,UAAU,sBAAsB,cAAc;AAClD,SAAoB,aAAAA,QAAM,cAAc,0BAA0B,UAAU;AAAA,IAC1E,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,QAAQ,UAAU;AAAA,IACpD,OAAO;AAAA,EACT,GAAG,QAAQ,CAAC;AACd;;;AC/CA,IAAAG,gBAAkB;AAElB,SAAS,cAAc;AACrB,MAAI,WAAW;AACf,SAAO;AAAA,IACL,YAAY,SAAS,aAAa;AAChC,iBAAW;AAAA,IACb;AAAA,IACA,OAAO,SAAS,QAAQ;AACtB,iBAAW;AAAA,IACb;AAAA,IACA,SAAS,SAAS,UAAU;AAC1B,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEA,IAAI,iCAA8C,cAAAC,QAAM,cAAc,YAAY,CAAC;AAE5E,IAAI,6BAA6B,SAASC,8BAA6B;AAC5E,SAAO,cAAAD,QAAM,WAAW,8BAA8B;AACxD;AAEO,IAAI,0BAA0B,SAASE,yBAAwB,MAAM;AAC1E,MAAI,WAAW,KAAK;AACpB,MAAI,QAAQ,cAAAF,QAAM,QAAQ,WAAY;AACpC,WAAO,YAAY;AAAA,EACrB,GAAG,CAAC,CAAC;AACL,SAAoB,cAAAA,QAAM,cAAc,+BAA+B,UAAU;AAAA,IAC/E;AAAA,EACF,GAAG,OAAO,aAAa,aAAa,SAAS,KAAK,IAAI,QAAQ;AAChE;;;AC/BA,IAAAG,gBAAkB;AAKlB,IAAI,kBAAkB,SAASC,iBAAgB,aAAa,SAAS,YAAY,eAAe;AAC9F,MAAI,gBAAgB,YAAY,WAAW,OAAO;AAElD,MAAI,eAAe,eAAe;AAChC,kBAAc,aAAa;AAAA,EAC7B;AACF;AAEO,SAAS,cAAc,MAAM,MAAM;AACxC,MAAI,aAAa,cAAAC,QAAM,OAAO,KAAK;AACnC,MAAI,cAAc,eAAe;AAEjC,MAAI,mBAAmB,gBAAgB,MAAM,IAAI,GAC7C,UAAU,iBAAiB,CAAC;AAEhC,MAAI,kBAAkB,cAAAA,QAAM,SAAS,YAAY,WAAW,OAAO,CAAC,GAChE,aAAa,gBAAgB,CAAC,GAC9B,gBAAgB,gBAAgB,CAAC;AAErC,MAAI,aAAa,cAAAA,QAAM,OAAO,OAAO;AACrC,aAAW,UAAU;AACrB,MAAI,gBAAgB,cAAAA,QAAM,OAAO,UAAU;AAC3C,gBAAc,UAAU;AACxB,gBAAAA,QAAM,UAAU,WAAY;AAC1B,eAAW,UAAU;AACrB,oBAAgB,aAAa,WAAW,SAAS,cAAc,SAAS,aAAa;AACrF,QAAI,cAAc,YAAY,cAAc,EAAE,UAAU,cAAc,WAAW,WAAY;AAC3F,UAAI,WAAW,SAAS;AACtB,wBAAgB,aAAa,WAAW,SAAS,cAAc,SAAS,aAAa;AAAA,MACvF;AAAA,IACF,CAAC,CAAC;AACF,WAAO,WAAY;AACjB,iBAAW,UAAU;AACrB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,SAAO;AACT;;;AC1CA,IAAAC,gBAAkB;AAIX,SAAS,cAAc,MAAM,MAAM;AACxC,MAAI,aAAa,cAAAC,QAAM,OAAO,KAAK;AACnC,MAAI,UAAU,wBAAwB,MAAM,IAAI;AAChD,MAAI,cAAc,eAAe;AAEjC,MAAI,kBAAkB,cAAAA,QAAM,SAAS,YAAY,WAAW,OAAO,CAAC,GAChE,aAAa,gBAAgB,CAAC,GAC9B,gBAAgB,gBAAgB,CAAC;AAErC,MAAI,aAAa,cAAAA,QAAM,OAAO,OAAO;AACrC,aAAW,UAAU;AACrB,MAAI,gBAAgB,cAAAA,QAAM,OAAO,UAAU;AAC3C,gBAAc,UAAU;AACxB,gBAAAA,QAAM,UAAU,WAAY;AAC1B,eAAW,UAAU;AACrB,QAAI,cAAc,YAAY,iBAAiB,EAAE,UAAU,cAAc,WAAW,WAAY;AAC9F,UAAI,WAAW,SAAS;AACtB,YAAI,gBAAgB,YAAY,WAAW,WAAW,OAAO;AAE7D,YAAI,cAAc,YAAY,eAAe;AAC3C,wBAAc,aAAa;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AACF,WAAO,WAAY;AACjB,iBAAW,UAAU;AACrB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,SAAO;AACT;;;AClCA;AACA,IAAAC,gBAAkB;;;ACDX,SAAS,iBAAiB,UAAU,mBAAmB,QAAQ;AAEpE,MAAI,OAAO,sBAAsB,YAAY;AAC3C,WAAO,kBAAkB,MAAM,QAAQ,MAAM;AAAA,EAC/C;AAGA,MAAI,OAAO,sBAAsB,UAAW,QAAO;AAEnD,SAAO,CAAC,CAAC;AACX;;;ADFO,SAAS,YAAY,MAAM,MAAM,MAAM;AAC5C,MAAI,aAAa,cAAAC,QAAM,OAAO,KAAK;AAEnC,MAAI,kBAAkB,cAAAA,QAAM,SAAS,CAAC,GAClC,cAAc,gBAAgB,CAAC;AAEnC,MAAI,UAAU,kBAAkB,MAAM,MAAM,IAAI;AAChD,MAAI,cAAc,eAAe;AACjC,MAAI,SAAS,cAAAA,QAAM,OAAO;AAE1B,MAAI,CAAC,OAAO,SAAS;AACnB,WAAO,UAAU,IAAI,iBAAiB,aAAa,OAAO;AAAA,EAC5D,OAAO;AACL,WAAO,QAAQ,WAAW,OAAO;AAAA,EACnC;AAEA,MAAI,gBAAgB,OAAO,QAAQ,iBAAiB;AACpD,gBAAAA,QAAM,UAAU,WAAY;AAC1B,eAAW,UAAU;AACrB,QAAI,cAAc,OAAO,QAAQ,UAAU,cAAc,WAAW,WAAY;AAC9E,UAAI,WAAW,SAAS;AACtB,oBAAY,SAAU,GAAG;AACvB,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF,CAAC,CAAC;AACF,WAAO,WAAY;AACjB,iBAAW,UAAU;AACrB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,CAAC;AACL,MAAI,SAAS,cAAAA,QAAM,YAAY,SAAU,WAAW,eAAe;AACjE,WAAO,QAAQ,OAAO,WAAW,aAAa,EAAE,MAAM,IAAI;AAAA,EAC5D,GAAG,CAAC,CAAC;AAEL,MAAI,cAAc,SAAS,iBAAiB,QAAW,OAAO,QAAQ,QAAQ,kBAAkB,CAAC,cAAc,KAAK,CAAC,GAAG;AACtH,UAAM,cAAc;AAAA,EACtB;AAEA,SAAO,SAAS,CAAC,GAAG,eAAe;AAAA,IACjC;AAAA,IACA,aAAa,cAAc;AAAA,EAC7B,CAAC;AACH;;;AEnDA,IAAAC,gBAAkB;AAKX,SAAS,aAAa,SAAS,UAAU;AAC9C,MAAI,aAAa,cAAAC,QAAM,OAAO,KAAK;AAEnC,MAAI,kBAAkB,cAAAA,QAAM,SAAS,CAAC,GAClC,cAAc,gBAAgB,CAAC;AAEnC,MAAI,cAAc,eAAe;AACjC,MAAI,qBAAqB,2BAA2B;AACpD,MAAI,mBAAmB,YAAY,4BAA4B,OAAO;AAEtE,mBAAiB,oBAAoB;AAErC,MAAI,iBAAiB,SAAS;AAC5B,qBAAiB,UAAU,cAAc,WAAW,iBAAiB,OAAO;AAAA,EAC9E;AAEA,MAAI,iBAAiB,WAAW;AAC9B,qBAAiB,YAAY,cAAc,WAAW,iBAAiB,SAAS;AAAA,EAClF;AAEA,MAAI,iBAAiB,WAAW;AAC9B,qBAAiB,YAAY,cAAc,WAAW,iBAAiB,SAAS;AAAA,EAClF;AAEA,MAAI,iBAAiB,UAAU;AAG7B,QAAI,OAAO,iBAAiB,cAAc,UAAU;AAClD,uBAAiB,YAAY;AAAA,IAC/B;AAIA,QAAI,iBAAiB,cAAc,GAAG;AACpC,uBAAiB,YAAY;AAAA,IAC/B;AAAA,EACF;AAEA,MAAI,iBAAiB,YAAY,iBAAiB,kBAAkB;AAElE,QAAI,CAAC,mBAAmB,QAAQ,GAAG;AACjC,uBAAiB,eAAe;AAAA,IAClC;AAAA,EACF;AAEA,MAAI,mBAAmB,cAAAA,QAAM,SAAS,WAAY;AAChD,WAAO,IAAI,SAAS,aAAa,gBAAgB;AAAA,EACnD,CAAC,GACG,WAAW,iBAAiB,CAAC;AAEjC,MAAI,SAAS,SAAS,oBAAoB,gBAAgB;AAC1D,gBAAAA,QAAM,UAAU,WAAY;AAC1B,eAAW,UAAU;AACrB,uBAAmB,WAAW;AAC9B,QAAI,cAAc,SAAS,UAAU,cAAc,WAAW,WAAY;AACxE,UAAI,WAAW,SAAS;AACtB,oBAAY,SAAU,GAAG;AACvB,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF,CAAC,CAAC;AAGF,aAAS,aAAa;AACtB,WAAO,WAAY;AACjB,iBAAW,UAAU;AACrB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,oBAAoB,QAAQ,CAAC;AACjC,gBAAAA,QAAM,UAAU,WAAY;AAG1B,aAAS,WAAW,kBAAkB;AAAA,MACpC,WAAW;AAAA,IACb,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAE/B,MAAI,iBAAiB,YAAY,OAAO,WAAW;AACjD,UAAM,SAAS,gBAAgB,gBAAgB,EAAE,KAAK,SAAU,MAAM;AACpE,UAAI,OAAO,KAAK;AAChB,uBAAiB,aAAa,OAAO,SAAS,iBAAiB,UAAU,IAAI;AAC7E,uBAAiB,aAAa,OAAO,SAAS,iBAAiB,UAAU,MAAM,IAAI;AAAA,IACrF,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,yBAAmB,WAAW;AAC9B,uBAAiB,WAAW,OAAO,SAAS,iBAAiB,QAAQ,KAAK;AAC1E,uBAAiB,aAAa,OAAO,SAAS,iBAAiB,UAAU,QAAW,KAAK;AAAA,IAC3F,CAAC;AAAA,EACH;AAGA,MAAI,OAAO,WAAW,CAAC,mBAAmB,QAAQ,KAAK,CAAC,OAAO,cAAc,iBAAiB,iBAAiB,UAAU,iBAAiB,kBAAkB,CAAC,OAAO,OAAO,SAAS,gBAAgB,CAAC,CAAC,GAAG;AACvM,UAAM,OAAO;AAAA,EACf;AAGA,MAAI,iBAAiB,wBAAwB,WAAW;AACtD,aAAS,SAAS,YAAY,QAAQ,gBAAgB;AAAA,EACxD;AAEA,SAAO;AACT;;;ACrGO,SAAS,SAAS,MAAM,MAAM,MAAM;AACzC,MAAI,gBAAgB,eAAe,MAAM,MAAM,IAAI;AACnD,SAAO,aAAa,eAAe,aAAa;AAClD;;;ACPA,IAAAC,gBAA+B;AAIxB,SAAS,WAAW,SAAS;AAClC,MAAI,aAAa,cAAAC,QAAM,OAAO,KAAK;AAEnC,MAAI,kBAAkB,cAAAA,QAAM,SAAS,CAAC,GAClC,cAAc,gBAAgB,CAAC;AAEnC,MAAI,cAAc,eAAe;AACjC,MAAI,uBAAmB,uBAAQ,WAAY;AACzC,WAAO,QAAQ,IAAI,SAAU,SAAS;AACpC,UAAI,mBAAmB,YAAY,4BAA4B,OAAO;AAEtE,uBAAiB,oBAAoB;AACrC,aAAO;AAAA,IACT,CAAC;AAAA,EACH,GAAG,CAAC,SAAS,WAAW,CAAC;AAEzB,MAAI,mBAAmB,cAAAA,QAAM,SAAS,WAAY;AAChD,WAAO,IAAI,gBAAgB,aAAa,gBAAgB;AAAA,EAC1D,CAAC,GACG,WAAW,iBAAiB,CAAC;AAEjC,MAAI,SAAS,SAAS,oBAAoB,gBAAgB;AAC1D,gBAAAA,QAAM,UAAU,WAAY;AAC1B,eAAW,UAAU;AACrB,QAAI,cAAc,SAAS,UAAU,cAAc,WAAW,WAAY;AACxE,UAAI,WAAW,SAAS;AACtB,oBAAY,SAAU,GAAG;AACvB,iBAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH;AAAA,IACF,CAAC,CAAC;AACF,WAAO,WAAY;AACjB,iBAAW,UAAU;AACrB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,QAAQ,CAAC;AACb,gBAAAA,QAAM,UAAU,WAAY;AAG1B,aAAS,WAAW,kBAAkB;AAAA,MACpC,WAAW;AAAA,IACb,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,QAAQ,CAAC;AAC/B,SAAO;AACT;;;AC5CO,SAAS,iBAAiB,MAAM,MAAM,MAAM;AACjD,MAAI,UAAU,eAAe,MAAM,MAAM,IAAI;AAC7C,SAAO,aAAa,SAAS,qBAAqB;AACpD;;;ACPA,IAAAC,gBAAkB;AAGX,SAAS,WAAW,OAAO,SAAS;AACzC,MAAI,cAAc,eAAe;AACjC,MAAI,aAAa,cAAAC,QAAM,OAAO,OAAO;AACrC,aAAW,UAAU;AAKrB,gBAAAA,QAAM,QAAQ,WAAY;AACxB,QAAI,OAAO;AACT,cAAQ,aAAa,OAAO,WAAW,OAAO;AAAA,IAChD;AAAA,EACF,GAAG,CAAC,aAAa,KAAK,CAAC;AACzB;AACO,IAAI,UAAU,SAASC,SAAQ,MAAM;AAC1C,MAAI,WAAW,KAAK,UAChB,UAAU,KAAK,SACf,QAAQ,KAAK;AACjB,aAAW,OAAO,OAAO;AACzB,SAAO;AACT;", "names": ["Subscribable", "FocusManager", "listener", "OnlineManager", "listener", "CancelledError", "<PERSON><PERSON><PERSON>", "resolve", "reject", "pause", "run", "cancelFn", "NotifyManager", "Query", "isStale", "fetchFn", "getDefaultState", "reducer", "Query<PERSON>ache", "Mutation", "MutationCache", "buildNewPages", "param", "fetchPage", "manual", "promise", "_loop", "i", "QueryClient", "QueryObserver", "trackProp", "QueriesObserver", "sortMatchesByOrderOfQueries", "InfiniteQueryObserver", "MutationObserver", "ReactDOM", "logger", "logger", "React", "useQueryClient", "QueryClientProvider", "import_react", "React", "useQueryErrorResetBoundary", "QueryErrorResetBoundary", "import_react", "checkIsFetching", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "import_react", "React", "Hydrate"]}