{"name": "whatsapp-api-gateway", "version": "1.0.0", "description": "Unofficial WhatsApp API Gateway using whatsapp-web.js", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "cd backend && npm start", "build": "cd frontend && npm run build", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test": "cd backend && npm test"}, "keywords": ["whatsapp", "api", "gateway", "whatsapp-web.js", "messaging"], "author": "WhatsApp API Gateway", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"socket.io-client": "^4.8.1"}}