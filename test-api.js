#!/usr/bin/env node

const http = require('http');

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testAPI() {
  console.log('🧪 Testing WhatsApp API Gateway...\n');

  try {
    // Test base API endpoint
    console.log('1. Testing /api endpoint:');
    const apiResponse = await makeRequest('/api');
    console.log(`   Status: ${apiResponse.status}`);
    console.log(`   Response: ${JSON.stringify(apiResponse.data, null, 2).substring(0, 200)}...\n`);

    // Test health endpoint
    console.log('2. Testing /health endpoint:');
    const healthResponse = await makeRequest('/health');
    console.log(`   Status: ${healthResponse.status}`);
    console.log(`   Response: ${JSON.stringify(healthResponse.data, null, 2)}\n`);

    // Test status endpoint
    console.log('3. Testing /api/status endpoint:');
    const statusResponse = await makeRequest('/api/status');
    console.log(`   Status: ${statusResponse.status}`);
    console.log(`   Response: ${JSON.stringify(statusResponse.data, null, 2)}\n`);

    // Test QR endpoint
    console.log('4. Testing /api/auth/qr endpoint:');
    const qrResponse = await makeRequest('/api/auth/qr');
    console.log(`   Status: ${qrResponse.status}`);
    if (qrResponse.data.qrCode) {
      console.log(`   QR Code: Available (${qrResponse.data.qrCode.length} characters)`);
    } else {
      console.log(`   Response: ${JSON.stringify(qrResponse.data, null, 2)}`);
    }
    console.log();

    // Test login endpoint
    console.log('5. Testing /api/auth/login endpoint:');
    const loginData = JSON.stringify({
      username: 'admin',
      password: 'password'
    });

    const loginOptions = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginData)
      }
    };

    const loginResponse = await new Promise((resolve, reject) => {
      const req = http.request(loginOptions, (res) => {
        let data = '';
        res.on('data', (chunk) => data += chunk);
        res.on('end', () => {
          try {
            resolve({ status: res.statusCode, data: JSON.parse(data) });
          } catch (error) {
            resolve({ status: res.statusCode, data: data });
          }
        });
      });
      req.on('error', reject);
      req.write(loginData);
      req.end();
    });

    console.log(`   Status: ${loginResponse.status}`);
    if (loginResponse.data.token) {
      console.log(`   Login: Successful`);
      console.log(`   Token: ${loginResponse.data.token.substring(0, 50)}...`);
      console.log(`   User: ${loginResponse.data.user.username}`);
    } else {
      console.log(`   Response: ${JSON.stringify(loginResponse.data, null, 2)}`);
    }

    console.log('\n✅ API testing completed successfully!');
    console.log('\n📱 Next steps:');
    console.log('   1. Open http://localhost:3000 in your browser (start frontend)');
    console.log('   2. Login with admin/password');
    console.log('   3. Scan the QR code with your WhatsApp mobile app');
    console.log('   4. Start sending messages!');

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure the backend server is running: npm run dev:backend');
    console.log('   2. Check if port 3001 is available');
    console.log('   3. Verify the server logs for any errors');
  }
}

testAPI();
