#!/usr/bin/env node

const { io } = require('socket.io-client');

console.log('🔌 Testing WebSocket connection...\n');

const socket = io('http://localhost:3001', {
  transports: ['websocket', 'polling'],
  timeout: 10000,
});

socket.on('connect', () => {
  console.log('✅ WebSocket connected successfully!');
  console.log('   Socket ID:', socket.id);
  
  // Test requesting status
  socket.emit('request_status');
  console.log('📤 Requested WhatsApp status');
});

socket.on('whatsapp_status', (status) => {
  console.log('📱 WhatsApp Status received:');
  console.log('   Ready:', status.isReady);
  console.log('   Authenticated:', status.isAuthenticated);
  console.log('   Has QR Code:', status.hasQRCode);
});

socket.on('qr', (qrCode) => {
  console.log('📱 QR Code received (length:', qrCode.length, 'characters)');
});

socket.on('authenticated', () => {
  console.log('✅ WhatsApp authenticated!');
});

socket.on('ready', () => {
  console.log('🚀 WhatsApp client is ready!');
});

socket.on('disconnect', (reason) => {
  console.log('❌ WebSocket disconnected:', reason);
});

socket.on('connect_error', (error) => {
  console.log('❌ WebSocket connection error:', error.message);
});

// Close after 5 seconds
setTimeout(() => {
  console.log('\n🔌 Closing WebSocket connection...');
  socket.close();
  process.exit(0);
}, 5000);
